#!/bin/bash

# Test deployment script for PDF Form Filler API

# Get the app URL from flyctl
APP_URL=$(flyctl info --json | jq -r '.Hostname')
if [ "$APP_URL" = "null" ] || [ -z "$APP_URL" ]; then
    echo "❌ Could not get app URL. Make sure the app is deployed."
    exit 1
fi

API_URL="https://$APP_URL"
echo "🚀 Testing PDF Form Filler API at: $API_URL"
echo "=" * 60

# Test 1: Health check
echo "1️⃣ Testing health endpoint..."
curl -s "$API_URL/health" | jq '.'
echo ""

# Test 2: Debug checkboxes
echo "2️⃣ Testing debug-checkboxes endpoint..."
curl -s "$API_URL/debug-checkboxes" | jq '.summary'
echo ""

# Test 3: Form validation
echo "3️⃣ Testing form validation..."
curl -s -X POST "$API_URL/validate-form" \
  -H "Content-Type: application/json" \
  -d '{
    "seller_name": "Test Seller",
    "buyer_name": "Test Buyer",
    "financing": {
      "cash": true,
      "conventional": false
    }
  }' | jq '.'
echo ""

# Test 4: PDF generation
echo "4️⃣ Testing PDF generation..."
RESPONSE=$(curl -s -X POST "$API_URL/fill-pdf" \
  -H "Content-Type: application/json" \
  -H "X-Debug-Checkboxes: true" \
  -d '{
    "seller_name": "Deployment Test Seller",
    "buyer_name": "Deployment Test Buyer",
    "purchase_price": 500000,
    "financing": {
      "cash": true,
      "conventional": false,
      "fha": true
    },
    "inspections": {
      "as_is_condition": true,
      "buyer_inspection": false
    }
  }' \
  --write-out "HTTPSTATUS:%{http_code}")

HTTP_STATUS=$(echo $RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $RESPONSE | sed -e 's/HTTPSTATUS\:.*//g')

if [ "$HTTP_STATUS" -eq 200 ]; then
    echo "✅ PDF generation successful (HTTP $HTTP_STATUS)"
    echo "📄 PDF size: $(echo $RESPONSE_BODY | wc -c) bytes"
    
    # Save the PDF for verification
    echo $RESPONSE_BODY > deployment_test.pdf
    echo "💾 PDF saved as deployment_test.pdf"
else
    echo "❌ PDF generation failed (HTTP $HTTP_STATUS)"
    echo "Response: $RESPONSE_BODY"
fi

echo ""
echo "🏁 Deployment test completed!"
echo "API URL: $API_URL"
