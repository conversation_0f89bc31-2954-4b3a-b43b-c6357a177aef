#!/usr/bin/env python3
"""
Debug PDF field values to understand why checkboxes aren't appearing as checked
"""

from pdfrw import PdfReader
import json
import os

def debug_pdf_fields(pdf_path: str):
    """Debug all fields in a PDF to understand their structure"""
    
    print(f"🔍 DEBUGGING PDF FIELDS: {pdf_path}")
    print("=" * 60)
    
    try:
        pdf = PdfReader(pdf_path)
        
        checkbox_fields = []
        text_fields = []
        other_fields = []
        
        for page_num, page in enumerate(pdf.pages, 1):
            if not page.Annots:
                continue
                
            print(f"\n📄 PAGE {page_num}")
            print("-" * 20)
            
            for annot_num, annot in enumerate(page.Annots, 1):
                if annot.Subtype == '/Widget':
                    field_name = getattr(annot, 'T', 'NO_NAME')
                    field_type = getattr(annot, 'FT', 'NO_TYPE')
                    field_value = getattr(annot, 'V', 'NO_VALUE')
                    appearance_state = getattr(annot, 'AS', 'NO_AS')
                    
                    field_info = {
                        'page': page_num,
                        'annot_num': annot_num,
                        'name': str(field_name),
                        'type': str(field_type),
                        'value': str(field_value),
                        'appearance_state': str(appearance_state)
                    }
                    
                    # Check for appearance dictionary
                    if hasattr(annot, 'AP') and annot.AP:
                        field_info['has_appearance_dict'] = True
                        if hasattr(annot.AP, 'N') and annot.AP.N:
                            field_info['appearance_keys'] = list(annot.AP.N.keys()) if hasattr(annot.AP.N, 'keys') else 'NO_KEYS'
                    else:
                        field_info['has_appearance_dict'] = False
                    
                    # Categorize fields
                    if field_type == '/Btn':
                        checkbox_fields.append(field_info)
                        
                        # Print detailed info for button fields
                        print(f"   🔲 CHECKBOX: {field_name}")
                        print(f"      Type: {field_type}")
                        print(f"      Value: {field_value}")
                        print(f"      AS: {appearance_state}")
                        print(f"      Has AP: {field_info['has_appearance_dict']}")
                        if field_info['has_appearance_dict']:
                            print(f"      AP Keys: {field_info.get('appearance_keys', 'N/A')}")
                        print()
                        
                    elif field_type == '/Tx':
                        text_fields.append(field_info)
                    else:
                        other_fields.append(field_info)
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 FIELD SUMMARY")
        print("=" * 60)
        print(f"🔲 Checkbox fields: {len(checkbox_fields)}")
        print(f"📝 Text fields: {len(text_fields)}")
        print(f"🔧 Other fields: {len(other_fields)}")
        
        # Analyze checkbox states
        checked_checkboxes = []
        unchecked_checkboxes = []
        
        for field in checkbox_fields:
            is_checked = (field['value'] in ['/Yes', '/On', '/1', 'Yes', 'On', '1'] or
                         field['appearance_state'] in ['/Yes', '/On', '/1', 'Yes', 'On', '1'])
            
            if is_checked:
                checked_checkboxes.append(field)
            else:
                unchecked_checkboxes.append(field)
        
        print(f"\n✅ Checked checkboxes: {len(checked_checkboxes)}")
        for field in checked_checkboxes[:5]:  # Show first 5
            print(f"   - {field['name']}: V={field['value']}, AS={field['appearance_state']}")
        
        print(f"\n☐ Unchecked checkboxes: {len(unchecked_checkboxes)}")
        for field in unchecked_checkboxes[:5]:  # Show first 5
            print(f"   - {field['name']}: V={field['value']}, AS={field['appearance_state']}")
        
        # Look for specific test checkboxes
        test_checkboxes = ['Checkbox_8', 'Checkbox_9', 'Checkbox_10', 'Checkbox_1', 'Checkbox_28']
        print(f"\n🎯 TEST CHECKBOX ANALYSIS")
        print("-" * 30)
        
        for test_checkbox in test_checkboxes:
            found = False
            for field in checkbox_fields:
                field_name_clean = field['name'].strip('()')
                if field_name_clean == test_checkbox:
                    print(f"✅ {test_checkbox}:")
                    print(f"   Value: {field['value']}")
                    print(f"   AS: {field['appearance_state']}")
                    print(f"   Has AP: {field['has_appearance_dict']}")
                    found = True
                    break
            
            if not found:
                print(f"❌ {test_checkbox}: NOT FOUND")
        
        return {
            'total_checkboxes': len(checkbox_fields),
            'checked_count': len(checked_checkboxes),
            'unchecked_count': len(unchecked_checkboxes),
            'checkbox_fields': checkbox_fields
        }
        
    except Exception as e:
        print(f"❌ Error debugging PDF: {e}")
        return None


def compare_before_after_pdfs():
    """Compare the original template with a filled PDF"""
    
    print("🔄 COMPARING TEMPLATE VS FILLED PDF")
    print("=" * 50)
    
    template_path = 'as_is_contract_fillable.pdf'
    filled_path = 'checkbox_test_1_20250715_165215.pdf'  # Use one of our test files
    
    print("\n📋 TEMPLATE PDF ANALYSIS")
    template_info = debug_pdf_fields(template_path)
    
    print("\n📋 FILLED PDF ANALYSIS")
    filled_info = debug_pdf_fields(filled_path)
    
    if template_info and filled_info:
        print("\n🔍 COMPARISON SUMMARY")
        print("-" * 25)
        print(f"Template checkboxes: {template_info['total_checkboxes']}")
        print(f"Filled checkboxes: {filled_info['total_checkboxes']}")
        print(f"Template checked: {template_info['checked_count']}")
        print(f"Filled checked: {filled_info['checked_count']}")


if __name__ == "__main__":
    print("🔍 PDF Field Debugging Tool")
    print("Analyzing checkbox field states in generated PDFs")
    print()
    
    # Debug a specific test PDF
    test_pdf = 'checkbox_test_1_20250715_165215.pdf'
    if os.path.exists(test_pdf):
        debug_pdf_fields(test_pdf)
    else:
        print(f"❌ Test PDF not found: {test_pdf}")
    
    print("\n" + "=" * 80)
    
    # Compare template vs filled
    compare_before_after_pdfs()
