2025-07-14 16:36:13,607 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 16:36:13,646 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 16:36:42,161 - werkzeug - INFO -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-14 16:36:43,721 - werkzeug - WARNING -  * Debu<PERSON> is active!
2025-07-14 16:36:43,734 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 16:37:10,074 - werkzeug - INFO -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-14 16:37:12,049 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 16:37:12,063 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 16:37:59,746 - werkzeug - INFO -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-14 16:38:03,014 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 16:38:03,034 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 16:38:41,556 - werkzeug - INFO -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-14 16:38:42,855 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 16:38:42,885 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 16:39:12,163 - werkzeug - INFO -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-14 16:39:13,409 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 16:39:13,410 - __main__ - INFO - Loaded field mapping with 0 fields
2025-07-14 16:39:13,411 - __main__ - INFO - Loaded field mapping guide
2025-07-14 16:39:13,411 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 16:39:13,411 - __main__ - INFO - No field mapping found, creating default from guide
2025-07-14 16:39:13,413 - __main__ - INFO - Field mapping saved successfully
2025-07-14 16:39:13,414 - __main__ - INFO - Default field mapping created from guide
2025-07-14 16:39:13,647 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 16:39:13,695 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 16:39:43,073 - werkzeug - INFO -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-14 16:39:44,598 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 16:39:44,598 - __main__ - INFO - Loaded field mapping with 14 fields
2025-07-14 16:39:44,600 - __main__ - INFO - Loaded field mapping guide
2025-07-14 16:39:44,601 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 16:39:44,801 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 16:39:44,840 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 16:40:10,141 - werkzeug - INFO -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-14 16:40:10,956 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 16:40:10,957 - __main__ - INFO - Loaded field mapping with 14 fields
2025-07-14 16:40:10,957 - __main__ - INFO - Loaded field mapping guide
2025-07-14 16:40:10,959 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 16:40:11,076 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 16:40:11,090 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 16:40:27,321 - werkzeug - INFO -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-14 16:40:29,035 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 16:40:29,035 - __main__ - INFO - Loaded field mapping with 14 fields
2025-07-14 16:40:29,036 - __main__ - INFO - Loaded field mapping guide
2025-07-14 16:40:29,037 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 16:40:29,107 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 16:40:29,118 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 16:40:58,407 - werkzeug - INFO -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-14 16:40:59,157 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 16:40:59,158 - __main__ - INFO - Loaded field mapping with 14 fields
2025-07-14 16:40:59,159 - __main__ - INFO - Loaded field mapping guide
2025-07-14 16:40:59,160 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 16:40:59,256 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 16:40:59,284 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 16:44:00,455 - app - INFO - Loaded 188 PDF field definitions
2025-07-14 16:44:00,457 - app - INFO - Loaded field mapping with 14 fields
2025-07-14 16:44:00,458 - app - INFO - Loaded field mapping guide
2025-07-14 16:44:00,459 - app - INFO - Loaded 188 PDF field definitions
2025-07-14 16:44:00,496 - app - INFO - Loaded field mapping with 2 fields
2025-07-14 16:44:00,497 - app - INFO - Loaded field mapping guide
2025-07-14 16:44:00,497 - app - INFO - Loaded 4 PDF field definitions
2025-07-14 16:44:00,501 - app - INFO - Loaded field mapping with 2 fields
2025-07-14 16:44:00,501 - app - INFO - Loaded field mapping guide
2025-07-14 16:44:00,503 - app - INFO - Loaded 4 PDF field definitions
2025-07-14 16:44:00,527 - app - INFO - Loaded field mapping with 2 fields
2025-07-14 16:44:00,527 - app - INFO - Loaded field mapping guide
2025-07-14 16:44:00,528 - app - INFO - Loaded 4 PDF field definitions
2025-07-14 19:21:03,945 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 19:21:03] "GET /health HTTP/1.1" 200 -
2025-07-14 19:21:04,054 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 19:21:04] "GET /extract-fields HTTP/1.1" 200 -
2025-07-14 19:21:04,059 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 19:21:04] "GET /get-mapping HTTP/1.1" 200 -
2025-07-14 19:21:04,070 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 19:21:04] "POST /validate-form HTTP/1.1" 200 -
2025-07-14 19:21:04,075 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 19:21:04] "GET /get-mapping HTTP/1.1" 200 -
2025-07-14 19:21:04,082 - __main__ - INFO - Received PDF fill request with 17 fields
2025-07-14 19:21:04,136 - __main__ - INFO - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-14 19:21:04,159 - __main__ - WARNING - No PDF field mapping found for: seller_phone
2025-07-14 19:21:04,159 - __main__ - WARNING - No PDF field mapping found for: seller_email
2025-07-14 19:21:04,159 - __main__ - WARNING - No PDF field mapping found for: buyer_phone
2025-07-14 19:21:04,248 - __main__ - INFO - PDF generated successfully. Processed: 22, Failed: 3
2025-07-14 19:21:04,248 - __main__ - INFO - PDF generated successfully. Fields processed: 22, Failed: 3
2025-07-14 19:21:04,249 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 19:21:04] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-14 22:18:36,385 - app - ERROR - PDF fields file not found: pdf_fields.json
2025-07-14 22:18:36,386 - app - INFO - Field mapping file not found, creating default structure
2025-07-14 22:18:36,387 - app - WARNING - Field mapping guide not found
2025-07-14 22:18:36,387 - app - WARNING - PDF fields info file not found
2025-07-14 22:18:36,387 - app - INFO - No field mapping found, creating default from guide
2025-07-14 22:18:36,387 - app - ERROR - No field mapping guide available
2025-07-14 22:22:38,520 - __main__ - ERROR - PDF fields file not found: pdf_fields.json
2025-07-14 22:22:38,522 - __main__ - INFO - Loaded field mapping with 115 fields
2025-07-14 22:22:38,522 - __main__ - WARNING - Field mapping guide not found
2025-07-14 22:22:38,522 - __main__ - WARNING - PDF fields info file not found
2025-07-14 22:22:38,557 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-14 22:22:38,557 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-14 22:22:38,567 - werkzeug - INFO -  * Restarting with stat
2025-07-14 22:22:38,846 - __main__ - ERROR - PDF fields file not found: pdf_fields.json
2025-07-14 22:22:38,846 - __main__ - INFO - Loaded field mapping with 115 fields
2025-07-14 22:22:38,846 - __main__ - WARNING - Field mapping guide not found
2025-07-14 22:22:38,847 - __main__ - WARNING - PDF fields info file not found
2025-07-14 22:22:38,860 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 22:22:38,877 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 22:23:08,133 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 22:23:08] "GET /extract-fields HTTP/1.1" 200 -
2025-07-14 22:23:08,183 - __main__ - INFO - Field mapping saved successfully
2025-07-14 22:23:08,184 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 22:23:08] "POST /update-mapping HTTP/1.1" 200 -
2025-07-14 22:23:08,190 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 22:23:08] "POST /validate-form HTTP/1.1" 200 -
2025-07-14 22:23:08,207 - __main__ - INFO - Received PDF fill request with 129 fields
2025-07-14 22:23:08,208 - __main__ - ERROR - Input validation failed: ["Mapped PDF field does not exist: '(Text_1)' (for form field: seller_name)", "Mapped PDF field does not exist: '(Text_2)' (for form field: buyer_name)", "Mapped PDF field does not exist: '(Text_3)' (for form field: property_address)", "Mapped PDF field does not exist: '(Text_4)' (for form field: county)", "Mapped PDF field does not exist: '(Text_5)' (for form field: tax_id)", "Mapped PDF field does not exist: '(Text_6)' (for form field: legal_description)", "Mapped PDF field does not exist: '(Text_7)' (for form field: additional_property_details)", "Mapped PDF field does not exist: '(Text_8)' (for form field: personal_property_included)", "Mapped PDF field does not exist: '(Text_9)' (for form field: personal_property_excluded)", "Mapped PDF field does not exist: '(Text_10)' (for form field: fixtures_included)", "Mapped PDF field does not exist: '(Text_11)' (for form field: other_items)", "Mapped PDF field does not exist: '(Number_1)' (for form field: purchase_price)", "Mapped PDF field does not exist: '(Number_2)' (for form field: initial_deposit)", "Mapped PDF field does not exist: '(Number_3)' (for form field: additional_deposit)", "Mapped PDF field does not exist: '(Number_4)' (for form field: loan_amount)", "Mapped PDF field does not exist: '(Number_5)' (for form field: down_payment)", "Mapped PDF field does not exist: '(Number_6)' (for form field: closing_costs)", "Mapped PDF field does not exist: '(Number_7)' (for form field: inspection_fee)", "Mapped PDF field does not exist: '(Number_8)' (for form field: appraisal_fee)", "Mapped PDF field does not exist: '(Number_9)' (for form field: survey_cost)", "Mapped PDF field does not exist: '(US_Phone_Number_1)' (for form field: seller_phone)", "Mapped PDF field does not exist: '(Email_1)' (for form field: seller_email)", "Mapped PDF field does not exist: '(US_Phone_Number_2)' (for form field: buyer_phone)", "Mapped PDF field does not exist: '(Date_1)' (for form field: contract_date)", "Mapped PDF field does not exist: '(Date_2)' (for form field: closing_date)", "Mapped PDF field does not exist: '(Date_3)' (for form field: inspection_deadline)", "Mapped PDF field does not exist: '(Date_4)' (for form field: financing_deadline)", "Mapped PDF field does not exist: '(Date_5)' (for form field: appraisal_deadline)", "Mapped PDF field does not exist: '(Text_12)' (for form field: seller_attorney)", "Mapped PDF field does not exist: '(Text_13)' (for form field: buyer_attorney)", "Mapped PDF field does not exist: '(Text_14)' (for form field: title_company)", "Mapped PDF field does not exist: '(Text_15)' (for form field: real_estate_agent_seller)", "Mapped PDF field does not exist: '(Text_16)' (for form field: real_estate_agent_buyer)", "Mapped PDF field does not exist: '(Text_17)' (for form field: lender_name)", "Mapped PDF field does not exist: '(Text_18)' (for form field: loan_officer)", "Mapped PDF field does not exist: '(Text_19)' (for form field: appraiser)", "Mapped PDF field does not exist: '(Text_20)' (for form field: inspector)", "Mapped PDF field does not exist: '(Text_21)' (for form field: surveyor)", "Mapped PDF field does not exist: '(Text_22)' (for form field: insurance_agent)", "Mapped PDF field does not exist: '(Text_23)' (for form field: hoa_management)", "Mapped PDF field does not exist: '(Text_24)' (for form field: utilities_electric)", "Mapped PDF field does not exist: '(Text_25)' (for form field: utilities_gas)", "Mapped PDF field does not exist: '(Text_26)' (for form field: utilities_water)", "Mapped PDF field does not exist: '(Text_27)' (for form field: utilities_cable)", "Mapped PDF field does not exist: '(Text_28)' (for form field: utilities_internet)", "Mapped PDF field does not exist: '(Text_29)' (for form field: utilities_trash)", "Mapped PDF field does not exist: '(Text_30)' (for form field: utilities_security)", "Mapped PDF field does not exist: '(Text_31)' (for form field: maintenance_pool)", "Mapped PDF field does not exist: '(Text_32)' (for form field: maintenance_landscape)", "Mapped PDF field does not exist: '(Text_33)' (for form field: maintenance_hvac)", "Mapped PDF field does not exist: '(Text_34)' (for form field: maintenance_dock)", "Mapped PDF field does not exist: '(Text_35)' (for form field: special_assessments)", "Mapped PDF field does not exist: '(Text_36)' (for form field: hoa_fees)", "Mapped PDF field does not exist: '(Text_37)' (for form field: property_taxes)", "Mapped PDF field does not exist: '(Text_38)' (for form field: insurance_cost)", "Mapped PDF field does not exist: '(Text_39)' (for form field: utility_costs)", "Mapped PDF field does not exist: '(Text_40)' (for form field: maintenance_costs)", "Mapped PDF field does not exist: '(Text_41)' (for form field: rental_restrictions)", "Mapped PDF field does not exist: '(Text_42)' (for form field: pet_restrictions)", "Mapped PDF field does not exist: '(Text_43)' (for form field: parking_spaces)", "Mapped PDF field does not exist: '(Text_44)' (for form field: storage_areas)", "Mapped PDF field does not exist: '(Text_45)' (for form field: outdoor_features)", "Mapped PDF field does not exist: '(Text_46)' (for form field: recent_improvements)", "Mapped PDF field does not exist: '(Text_47)' (for form field: warranty_information)", "Mapped PDF field does not exist: '(Text_48)' (for form field: disclosure_items)", "Mapped PDF field does not exist: '(Text_49)' (for form field: additional_terms)", "Mapped PDF field does not exist: '(Text_50)' (for form field: contingencies)", "Mapped PDF field does not exist: '(Text_51)' (for form field: special_conditions)", "Mapped PDF field does not exist: '(Text_52)' (for form field: possession_date)", "Mapped PDF field does not exist: '(Text_53)' (for form field: key_information)", "Mapped PDF field does not exist: '(Text_54)' (for form field: alarm_codes)", "Mapped PDF field does not exist: '(Text_55)' (for form field: utility_transfers)", "Mapped PDF field does not exist: '(Text_56)' (for form field: final_walkthrough)", "Mapped PDF field does not exist: '(Text_57)' (for form field: document_delivery)", "Mapped PDF field does not exist: '(Text_58)' (for form field: communication_preferences)", "Mapped PDF field does not exist: '(Initials_1)' (for form field: buyer_initial_1)", "Mapped PDF field does not exist: '(Initials_2)' (for form field: buyer_initial_2)", "Mapped PDF field does not exist: '(Initials_3)' (for form field: buyer_initial_3)", "Mapped PDF field does not exist: '(Initials_4)' (for form field: buyer_initial_4)", "Mapped PDF field does not exist: '(Initials_5)' (for form field: buyer_initial_5)", "Mapped PDF field does not exist: '(Initials_6)' (for form field: buyer_initial_6)", "Mapped PDF field does not exist: '(Initials_7)' (for form field: buyer_initial_7)", "Mapped PDF field does not exist: '(Initials_8)' (for form field: buyer_initial_8)", "Mapped PDF field does not exist: '(Initials_9)' (for form field: buyer_initial_9)", "Mapped PDF field does not exist: '(Initials_10)' (for form field: buyer_initial_10)", "Mapped PDF field does not exist: '(Initials_11)' (for form field: buyer_initial_11)", "Mapped PDF field does not exist: '(Initials_12)' (for form field: buyer_initial_12)", "Mapped PDF field does not exist: '(Initials_13)' (for form field: buyer_initial_13)", "Mapped PDF field does not exist: '(Initials_14)' (for form field: buyer_initial_14)", "Mapped PDF field does not exist: '(Initials_15)' (for form field: buyer_initial_15)", "Mapped PDF field does not exist: '(Initials_16)' (for form field: buyer_initial_16)", "Mapped PDF field does not exist: '(Initials_17)' (for form field: buyer_initial_17)", "Mapped PDF field does not exist: '(Initials_18)' (for form field: buyer_initial_18)", "Mapped PDF field does not exist: '(Initials_19)' (for form field: seller_initial_1)", "Mapped PDF field does not exist: '(Initials_20)' (for form field: seller_initial_2)", "Mapped PDF field does not exist: '(Initials_21)' (for form field: seller_initial_3)", "Mapped PDF field does not exist: '(Initials_22)' (for form field: seller_initial_4)", "Mapped PDF field does not exist: '(Initials_23)' (for form field: seller_initial_5)", "Mapped PDF field does not exist: '(Initials_24)' (for form field: seller_initial_6)", "Mapped PDF field does not exist: '(Initials_25)' (for form field: seller_initial_7)", "Mapped PDF field does not exist: '(Initials_26)' (for form field: seller_initial_8)", "Mapped PDF field does not exist: '(Initials_27)' (for form field: seller_initial_9)", "Mapped PDF field does not exist: '(Initials_28)' (for form field: seller_initial_10)", "Mapped PDF field does not exist: '(Initials_29)' (for form field: seller_initial_11)", "Mapped PDF field does not exist: '(Initials_30)' (for form field: seller_initial_12)", "Mapped PDF field does not exist: '(Initials_31)' (for form field: seller_initial_13)", "Mapped PDF field does not exist: '(Initials_32)' (for form field: seller_initial_14)", "Mapped PDF field does not exist: '(Initials_33)' (for form field: seller_initial_15)", "Mapped PDF field does not exist: '(Initials_34)' (for form field: seller_initial_16)", "Mapped PDF field does not exist: '(Initials_35)' (for form field: seller_initial_17)", "Mapped PDF field does not exist: '(Initials_36)' (for form field: seller_initial_18)", "Mapped PDF field does not exist: '(Signature_1)' (for form field: buyer_signature_1)", "Mapped PDF field does not exist: '(Signature_2)' (for form field: buyer_signature_2)", "Mapped PDF field does not exist: '(Signature_3)' (for form field: seller_signature_1)", "Mapped PDF field does not exist: '(Signature_4)' (for form field: seller_signature_2)"]
2025-07-14 22:23:08,218 - __main__ - ERROR - PDF generation failed: ["Mapped PDF field does not exist: '(Text_1)' (for form field: seller_name)", "Mapped PDF field does not exist: '(Text_2)' (for form field: buyer_name)", "Mapped PDF field does not exist: '(Text_3)' (for form field: property_address)", "Mapped PDF field does not exist: '(Text_4)' (for form field: county)", "Mapped PDF field does not exist: '(Text_5)' (for form field: tax_id)", "Mapped PDF field does not exist: '(Text_6)' (for form field: legal_description)", "Mapped PDF field does not exist: '(Text_7)' (for form field: additional_property_details)", "Mapped PDF field does not exist: '(Text_8)' (for form field: personal_property_included)", "Mapped PDF field does not exist: '(Text_9)' (for form field: personal_property_excluded)", "Mapped PDF field does not exist: '(Text_10)' (for form field: fixtures_included)", "Mapped PDF field does not exist: '(Text_11)' (for form field: other_items)", "Mapped PDF field does not exist: '(Number_1)' (for form field: purchase_price)", "Mapped PDF field does not exist: '(Number_2)' (for form field: initial_deposit)", "Mapped PDF field does not exist: '(Number_3)' (for form field: additional_deposit)", "Mapped PDF field does not exist: '(Number_4)' (for form field: loan_amount)", "Mapped PDF field does not exist: '(Number_5)' (for form field: down_payment)", "Mapped PDF field does not exist: '(Number_6)' (for form field: closing_costs)", "Mapped PDF field does not exist: '(Number_7)' (for form field: inspection_fee)", "Mapped PDF field does not exist: '(Number_8)' (for form field: appraisal_fee)", "Mapped PDF field does not exist: '(Number_9)' (for form field: survey_cost)", "Mapped PDF field does not exist: '(US_Phone_Number_1)' (for form field: seller_phone)", "Mapped PDF field does not exist: '(Email_1)' (for form field: seller_email)", "Mapped PDF field does not exist: '(US_Phone_Number_2)' (for form field: buyer_phone)", "Mapped PDF field does not exist: '(Date_1)' (for form field: contract_date)", "Mapped PDF field does not exist: '(Date_2)' (for form field: closing_date)", "Mapped PDF field does not exist: '(Date_3)' (for form field: inspection_deadline)", "Mapped PDF field does not exist: '(Date_4)' (for form field: financing_deadline)", "Mapped PDF field does not exist: '(Date_5)' (for form field: appraisal_deadline)", "Mapped PDF field does not exist: '(Text_12)' (for form field: seller_attorney)", "Mapped PDF field does not exist: '(Text_13)' (for form field: buyer_attorney)", "Mapped PDF field does not exist: '(Text_14)' (for form field: title_company)", "Mapped PDF field does not exist: '(Text_15)' (for form field: real_estate_agent_seller)", "Mapped PDF field does not exist: '(Text_16)' (for form field: real_estate_agent_buyer)", "Mapped PDF field does not exist: '(Text_17)' (for form field: lender_name)", "Mapped PDF field does not exist: '(Text_18)' (for form field: loan_officer)", "Mapped PDF field does not exist: '(Text_19)' (for form field: appraiser)", "Mapped PDF field does not exist: '(Text_20)' (for form field: inspector)", "Mapped PDF field does not exist: '(Text_21)' (for form field: surveyor)", "Mapped PDF field does not exist: '(Text_22)' (for form field: insurance_agent)", "Mapped PDF field does not exist: '(Text_23)' (for form field: hoa_management)", "Mapped PDF field does not exist: '(Text_24)' (for form field: utilities_electric)", "Mapped PDF field does not exist: '(Text_25)' (for form field: utilities_gas)", "Mapped PDF field does not exist: '(Text_26)' (for form field: utilities_water)", "Mapped PDF field does not exist: '(Text_27)' (for form field: utilities_cable)", "Mapped PDF field does not exist: '(Text_28)' (for form field: utilities_internet)", "Mapped PDF field does not exist: '(Text_29)' (for form field: utilities_trash)", "Mapped PDF field does not exist: '(Text_30)' (for form field: utilities_security)", "Mapped PDF field does not exist: '(Text_31)' (for form field: maintenance_pool)", "Mapped PDF field does not exist: '(Text_32)' (for form field: maintenance_landscape)", "Mapped PDF field does not exist: '(Text_33)' (for form field: maintenance_hvac)", "Mapped PDF field does not exist: '(Text_34)' (for form field: maintenance_dock)", "Mapped PDF field does not exist: '(Text_35)' (for form field: special_assessments)", "Mapped PDF field does not exist: '(Text_36)' (for form field: hoa_fees)", "Mapped PDF field does not exist: '(Text_37)' (for form field: property_taxes)", "Mapped PDF field does not exist: '(Text_38)' (for form field: insurance_cost)", "Mapped PDF field does not exist: '(Text_39)' (for form field: utility_costs)", "Mapped PDF field does not exist: '(Text_40)' (for form field: maintenance_costs)", "Mapped PDF field does not exist: '(Text_41)' (for form field: rental_restrictions)", "Mapped PDF field does not exist: '(Text_42)' (for form field: pet_restrictions)", "Mapped PDF field does not exist: '(Text_43)' (for form field: parking_spaces)", "Mapped PDF field does not exist: '(Text_44)' (for form field: storage_areas)", "Mapped PDF field does not exist: '(Text_45)' (for form field: outdoor_features)", "Mapped PDF field does not exist: '(Text_46)' (for form field: recent_improvements)", "Mapped PDF field does not exist: '(Text_47)' (for form field: warranty_information)", "Mapped PDF field does not exist: '(Text_48)' (for form field: disclosure_items)", "Mapped PDF field does not exist: '(Text_49)' (for form field: additional_terms)", "Mapped PDF field does not exist: '(Text_50)' (for form field: contingencies)", "Mapped PDF field does not exist: '(Text_51)' (for form field: special_conditions)", "Mapped PDF field does not exist: '(Text_52)' (for form field: possession_date)", "Mapped PDF field does not exist: '(Text_53)' (for form field: key_information)", "Mapped PDF field does not exist: '(Text_54)' (for form field: alarm_codes)", "Mapped PDF field does not exist: '(Text_55)' (for form field: utility_transfers)", "Mapped PDF field does not exist: '(Text_56)' (for form field: final_walkthrough)", "Mapped PDF field does not exist: '(Text_57)' (for form field: document_delivery)", "Mapped PDF field does not exist: '(Text_58)' (for form field: communication_preferences)", "Mapped PDF field does not exist: '(Initials_1)' (for form field: buyer_initial_1)", "Mapped PDF field does not exist: '(Initials_2)' (for form field: buyer_initial_2)", "Mapped PDF field does not exist: '(Initials_3)' (for form field: buyer_initial_3)", "Mapped PDF field does not exist: '(Initials_4)' (for form field: buyer_initial_4)", "Mapped PDF field does not exist: '(Initials_5)' (for form field: buyer_initial_5)", "Mapped PDF field does not exist: '(Initials_6)' (for form field: buyer_initial_6)", "Mapped PDF field does not exist: '(Initials_7)' (for form field: buyer_initial_7)", "Mapped PDF field does not exist: '(Initials_8)' (for form field: buyer_initial_8)", "Mapped PDF field does not exist: '(Initials_9)' (for form field: buyer_initial_9)", "Mapped PDF field does not exist: '(Initials_10)' (for form field: buyer_initial_10)", "Mapped PDF field does not exist: '(Initials_11)' (for form field: buyer_initial_11)", "Mapped PDF field does not exist: '(Initials_12)' (for form field: buyer_initial_12)", "Mapped PDF field does not exist: '(Initials_13)' (for form field: buyer_initial_13)", "Mapped PDF field does not exist: '(Initials_14)' (for form field: buyer_initial_14)", "Mapped PDF field does not exist: '(Initials_15)' (for form field: buyer_initial_15)", "Mapped PDF field does not exist: '(Initials_16)' (for form field: buyer_initial_16)", "Mapped PDF field does not exist: '(Initials_17)' (for form field: buyer_initial_17)", "Mapped PDF field does not exist: '(Initials_18)' (for form field: buyer_initial_18)", "Mapped PDF field does not exist: '(Initials_19)' (for form field: seller_initial_1)", "Mapped PDF field does not exist: '(Initials_20)' (for form field: seller_initial_2)", "Mapped PDF field does not exist: '(Initials_21)' (for form field: seller_initial_3)", "Mapped PDF field does not exist: '(Initials_22)' (for form field: seller_initial_4)", "Mapped PDF field does not exist: '(Initials_23)' (for form field: seller_initial_5)", "Mapped PDF field does not exist: '(Initials_24)' (for form field: seller_initial_6)", "Mapped PDF field does not exist: '(Initials_25)' (for form field: seller_initial_7)", "Mapped PDF field does not exist: '(Initials_26)' (for form field: seller_initial_8)", "Mapped PDF field does not exist: '(Initials_27)' (for form field: seller_initial_9)", "Mapped PDF field does not exist: '(Initials_28)' (for form field: seller_initial_10)", "Mapped PDF field does not exist: '(Initials_29)' (for form field: seller_initial_11)", "Mapped PDF field does not exist: '(Initials_30)' (for form field: seller_initial_12)", "Mapped PDF field does not exist: '(Initials_31)' (for form field: seller_initial_13)", "Mapped PDF field does not exist: '(Initials_32)' (for form field: seller_initial_14)", "Mapped PDF field does not exist: '(Initials_33)' (for form field: seller_initial_15)", "Mapped PDF field does not exist: '(Initials_34)' (for form field: seller_initial_16)", "Mapped PDF field does not exist: '(Initials_35)' (for form field: seller_initial_17)", "Mapped PDF field does not exist: '(Initials_36)' (for form field: seller_initial_18)", "Mapped PDF field does not exist: '(Signature_1)' (for form field: buyer_signature_1)", "Mapped PDF field does not exist: '(Signature_2)' (for form field: buyer_signature_2)", "Mapped PDF field does not exist: '(Signature_3)' (for form field: seller_signature_1)", "Mapped PDF field does not exist: '(Signature_4)' (for form field: seller_signature_2)"]
2025-07-14 22:23:08,219 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 22:23:08] "[35m[1mPOST /fill-pdf HTTP/1.1[0m" 500 -
2025-07-14 22:23:40,884 - werkzeug - INFO -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/comprehensive_test.py', reloading
2025-07-14 22:23:41,356 - werkzeug - INFO -  * Restarting with stat
2025-07-14 22:23:42,436 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 22:23:42,437 - __main__ - INFO - Loaded field mapping with 115 fields
2025-07-14 22:23:42,438 - __main__ - WARNING - Field mapping guide not found
2025-07-14 22:23:42,438 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 22:23:42,482 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 22:23:42,502 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 22:24:10,918 - werkzeug - INFO -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/comprehensive_test.py', reloading
2025-07-14 22:24:11,155 - werkzeug - INFO -  * Restarting with stat
2025-07-14 22:24:11,714 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 22:24:11,714 - __main__ - INFO - Loaded field mapping with 115 fields
2025-07-14 22:24:11,714 - __main__ - WARNING - Field mapping guide not found
2025-07-14 22:24:11,715 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 22:24:11,749 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 22:24:11,765 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 22:24:42,288 - werkzeug - INFO -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/comprehensive_test.py', reloading
2025-07-14 22:24:42,758 - werkzeug - INFO -  * Restarting with stat
2025-07-14 22:24:43,675 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 22:24:43,676 - __main__ - INFO - Loaded field mapping with 115 fields
2025-07-14 22:24:43,677 - __main__ - WARNING - Field mapping guide not found
2025-07-14 22:24:43,677 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 22:24:43,736 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 22:24:43,768 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 22:25:19,211 - werkzeug - INFO -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/comprehensive_test.py', reloading
2025-07-14 22:25:19,269 - werkzeug - INFO -  * Restarting with stat
2025-07-14 22:25:20,132 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 22:25:20,133 - __main__ - INFO - Loaded field mapping with 115 fields
2025-07-14 22:25:20,133 - __main__ - WARNING - Field mapping guide not found
2025-07-14 22:25:20,134 - __main__ - INFO - Loaded 188 PDF field definitions
2025-07-14 22:25:20,160 - werkzeug - WARNING -  * Debugger is active!
2025-07-14 22:25:20,182 - werkzeug - INFO -  * Debugger PIN: 405-311-689
2025-07-14 22:25:40,618 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 22:25:40] "GET /extract-fields HTTP/1.1" 200 -
2025-07-14 22:25:40,634 - __main__ - INFO - Field mapping saved successfully
2025-07-14 22:25:40,634 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 22:25:40] "POST /update-mapping HTTP/1.1" 200 -
2025-07-14 22:25:40,640 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 22:25:40] "POST /validate-form HTTP/1.1" 200 -
2025-07-14 22:25:40,645 - __main__ - INFO - Received PDF fill request with 129 fields
2025-07-14 22:25:40,702 - __main__ - INFO - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-14 22:25:40,971 - __main__ - WARNING - No PDF field mapping found for: signature_date_buyer_1
2025-07-14 22:25:40,971 - __main__ - WARNING - No PDF field mapping found for: signature_date_buyer_2
2025-07-14 22:25:40,971 - __main__ - WARNING - No PDF field mapping found for: signature_date_seller_1
2025-07-14 22:25:40,971 - __main__ - WARNING - No PDF field mapping found for: signature_date_seller_2
2025-07-14 22:25:40,971 - __main__ - WARNING - No PDF field mapping found for: notary_signature
2025-07-14 22:25:40,972 - __main__ - WARNING - No PDF field mapping found for: notary_date
2025-07-14 22:25:40,972 - __main__ - WARNING - No PDF field mapping found for: witness_1
2025-07-14 22:25:40,972 - __main__ - WARNING - No PDF field mapping found for: witness_2
2025-07-14 22:25:40,972 - __main__ - WARNING - No PDF field mapping found for: witness_date_1
2025-07-14 22:25:40,972 - __main__ - WARNING - No PDF field mapping found for: witness_date_2
2025-07-14 22:25:41,056 - __main__ - INFO - PDF generated successfully. Processed: 173, Failed: 10
2025-07-14 22:25:41,056 - __main__ - INFO - PDF generated successfully. Fields processed: 173, Failed: 10
2025-07-14 22:25:41,057 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 22:25:41] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:12:23,389 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 16:12:23,394 - __main__ - INFO - _load_field_mapping:564 - Loaded field mapping with 115 fields
2025-07-15 16:12:23,395 - __main__ - WARNING - _load_field_mapping_guide:582 - Field mapping guide not found
2025-07-15 16:12:23,399 - __main__ - INFO - _load_pdf_fields_info:594 - Loaded 188 PDF field definitions
2025-07-15 16:12:23,504 - werkzeug - INFO - _log:187 - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-15 16:12:23,504 - werkzeug - INFO - _log:187 - [33mPress CTRL+C to quit[0m
2025-07-15 16:12:23,522 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 16:12:23,792 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 16:12:23,792 - __main__ - INFO - _load_field_mapping:564 - Loaded field mapping with 115 fields
2025-07-15 16:12:23,793 - __main__ - WARNING - _load_field_mapping_guide:582 - Field mapping guide not found
2025-07-15 16:12:23,793 - __main__ - INFO - _load_pdf_fields_info:594 - Loaded 188 PDF field definitions
2025-07-15 16:12:23,809 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 16:12:23,832 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 16:12:42,685 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:12:42] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:12:42,711 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 5 fields
2025-07-15 16:12:42,884 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:12:42,890 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:42,890 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:42,891 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:12:42,897 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:42,897 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:42,897 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:12:42,900 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:42,900 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:42,900 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:12:42,902 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:42,902 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:42,903 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:12:42,914 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_1) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:42,914 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:42,915 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_1) to True
2025-07-15 16:12:42,916 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_3) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:42,917 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:42,917 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_3) to True
2025-07-15 16:12:42,919 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_4) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:42,919 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:42,923 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_4) to False
2025-07-15 16:12:43,000 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 10, Failed: 0
2025-07-15 16:12:43,000 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 10, Failed: 0
2025-07-15 16:12:43,001 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:12:43] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:12:43,033 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:12:43] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:12:43,044 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 5 fields
2025-07-15 16:12:43,102 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:12:43,108 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,109 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,109 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:12:43,112 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,112 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,112 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:12:43,114 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,114 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,114 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:12:43,117 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,117 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,117 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:12:43,119 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_1) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,120 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,127 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_1) to True
2025-07-15 16:12:43,130 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_3) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,130 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,130 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_3) to True
2025-07-15 16:12:43,133 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_4) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,133 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,133 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_4) to False
2025-07-15 16:12:43,216 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 10, Failed: 0
2025-07-15 16:12:43,216 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 10, Failed: 0
2025-07-15 16:12:43,216 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:12:43] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:12:43,251 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:12:43] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:12:43,257 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 5 fields
2025-07-15 16:12:43,322 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:12:43,338 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,339 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,339 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:12:43,341 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,341 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,341 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:12:43,346 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,347 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,347 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:12:43,349 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,349 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,349 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:12:43,351 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_1) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,351 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,354 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_1) to True
2025-07-15 16:12:43,361 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_3) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,361 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,361 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_3) to True
2025-07-15 16:12:43,364 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_4) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,364 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,364 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_4) to False
2025-07-15 16:12:43,434 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 10, Failed: 0
2025-07-15 16:12:43,434 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 10, Failed: 0
2025-07-15 16:12:43,434 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:12:43] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:12:43,462 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:12:43] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:12:43,468 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 6 fields
2025-07-15 16:12:43,523 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:12:43,529 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,529 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,530 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:12:43,531 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,531 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,532 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:12:43,533 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,534 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,534 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:12:43,540 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,541 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,541 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:12:43,543 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_1) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,543 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,543 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_1) to True
2025-07-15 16:12:43,546 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_3) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,547 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,547 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_3) to True
2025-07-15 16:12:43,548 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_4) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,549 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,549 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_4) to False
2025-07-15 16:12:43,551 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_28) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,551 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_28): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,551 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_28) to True
2025-07-15 16:12:43,556 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_29) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,556 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_29): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,556 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_29) to True
2025-07-15 16:12:43,559 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_30) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,559 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_30): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,559 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_30) to True
2025-07-15 16:12:43,561 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_31) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,561 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_31): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,561 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_31) to True
2025-07-15 16:12:43,565 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_32) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,565 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_32): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,565 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_32) to False
2025-07-15 16:12:43,567 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_33) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,567 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_33): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,568 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_33) to False
2025-07-15 16:12:43,655 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 16, Failed: 0
2025-07-15 16:12:43,655 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 16, Failed: 0
2025-07-15 16:12:43,656 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:12:43] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:12:43,680 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:12:43] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:12:43,689 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:12:43] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:12:43,694 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:12:43] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:12:43,700 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 6 fields
2025-07-15 16:12:43,700 - __main__ - INFO - fill_pdf:1102 - Checkbox debug mode enabled
2025-07-15 16:12:43,767 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:12:43,767 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Text_1)' = Debug Test Seller
2025-07-15 16:12:43,769 - __main__ - DEBUG - _fill_regular_field:981 - Successfully filled field: seller_name -> '(Text_1)' = Debug Test Seller
2025-07-15 16:12:43,770 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Text_2)' = Debug Test Buyer
2025-07-15 16:12:43,772 - __main__ - DEBUG - _fill_regular_field:981 - Successfully filled field: buyer_name -> '(Text_2)' = Debug Test Buyer
2025-07-15 16:12:43,773 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Number_1)' = 750000
2025-07-15 16:12:43,774 - __main__ - DEBUG - _fill_regular_field:981 - Successfully filled field: purchase_price -> '(Number_1)' = 750000
2025-07-15 16:12:43,774 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_8)
2025-07-15 16:12:43,774 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_8) has appearance dictionary
2025-07-15 16:12:43,775 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_8) normal appearance keys: ['/Checkbox_8', '/Off']
2025-07-15 16:12:43,775 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,775 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_8): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:43,775 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:12:43,775 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_8) current state - V: None, AS: /Off
2025-07-15 16:12:43,775 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_8)
2025-07-15 16:12:43,775 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,775 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_8): V=/Yes, AS=/Yes
2025-07-15 16:12:43,775 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_8) new state - V: /Yes, AS: /Yes
2025-07-15 16:12:43,775 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:12:43,775 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_8)' = True
2025-07-15 16:12:43,777 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: financing.cash -> '(Checkbox_8)' = True
2025-07-15 16:12:43,777 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_9)
2025-07-15 16:12:43,779 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_9) has appearance dictionary
2025-07-15 16:12:43,779 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_9) normal appearance keys: ['/Checkbox_9', '/Off']
2025-07-15 16:12:43,780 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,780 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_9): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:43,780 - __main__ - DEBUG - _handle_button_field:154 - Converted input value False to boolean: False
2025-07-15 16:12:43,780 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_9) current state - V: None, AS: /Off
2025-07-15 16:12:43,780 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_9)
2025-07-15 16:12:43,780 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,780 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_9): V=/Off, AS=/Off
2025-07-15 16:12:43,780 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_9) new state - V: /Off, AS: /Off
2025-07-15 16:12:43,780 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:12:43,780 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_9)' = False
2025-07-15 16:12:43,782 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: financing.conventional -> '(Checkbox_9)' = False
2025-07-15 16:12:43,782 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_10)
2025-07-15 16:12:43,783 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_10) has appearance dictionary
2025-07-15 16:12:43,784 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_10) normal appearance keys: ['/Checkbox_10', '/Off']
2025-07-15 16:12:43,784 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,784 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_10): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:43,784 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:12:43,784 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_10) current state - V: None, AS: /Off
2025-07-15 16:12:43,785 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_10)
2025-07-15 16:12:43,785 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,785 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_10): V=/Yes, AS=/Yes
2025-07-15 16:12:43,785 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_10) new state - V: /Yes, AS: /Yes
2025-07-15 16:12:43,785 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:12:43,785 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_10)' = True
2025-07-15 16:12:43,791 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: financing.fha -> '(Checkbox_10)' = True
2025-07-15 16:12:43,791 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_11)
2025-07-15 16:12:43,791 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_11) has appearance dictionary
2025-07-15 16:12:43,792 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_11) normal appearance keys: ['/Checkbox_11', '/Off']
2025-07-15 16:12:43,792 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,792 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_11): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:43,792 - __main__ - DEBUG - _handle_button_field:154 - Converted input value False to boolean: False
2025-07-15 16:12:43,792 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_11) current state - V: None, AS: /Off
2025-07-15 16:12:43,792 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_11)
2025-07-15 16:12:43,792 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,793 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_11): V=/Off, AS=/Off
2025-07-15 16:12:43,793 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_11) new state - V: /Off, AS: /Off
2025-07-15 16:12:43,793 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:12:43,793 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_11)' = False
2025-07-15 16:12:43,796 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: financing.va -> '(Checkbox_11)' = False
2025-07-15 16:12:43,797 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_12)
2025-07-15 16:12:43,797 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_12) has appearance dictionary
2025-07-15 16:12:43,798 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_12) normal appearance keys: ['/Checkbox_12', '/Off']
2025-07-15 16:12:43,798 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_12) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,798 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_12): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:43,798 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:12:43,799 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_12) current state - V: None, AS: /Off
2025-07-15 16:12:43,799 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_12)
2025-07-15 16:12:43,799 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_12): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,799 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_12): V=/Yes, AS=/Yes
2025-07-15 16:12:43,799 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_12) new state - V: /Yes, AS: /Yes
2025-07-15 16:12:43,800 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_12) to True
2025-07-15 16:12:43,800 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_12)' = True
2025-07-15 16:12:43,802 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: financing.usda -> '(Checkbox_12)' = True
2025-07-15 16:12:43,806 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_13)
2025-07-15 16:12:43,910 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_13) has appearance dictionary
2025-07-15 16:12:43,910 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_13) normal appearance keys: ['/Checkbox_13', '/Off']
2025-07-15 16:12:43,911 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_13) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,911 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_13): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:43,911 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:12:43,911 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_13) current state - V: None, AS: /Off
2025-07-15 16:12:43,911 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_13)
2025-07-15 16:12:43,939 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_13): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,939 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_13): V=/Yes, AS=/Yes
2025-07-15 16:12:43,939 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_13) new state - V: /Yes, AS: /Yes
2025-07-15 16:12:43,939 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_13) to True
2025-07-15 16:12:43,939 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_13)' = True
2025-07-15 16:12:43,941 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: financing.other -> '(Checkbox_13)' = True
2025-07-15 16:12:43,941 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_1)
2025-07-15 16:12:43,941 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_1) has appearance dictionary
2025-07-15 16:12:43,942 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_1) normal appearance keys: ['/Checkbox_1', '/Off']
2025-07-15 16:12:43,943 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_1) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,943 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_1): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:43,943 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:12:43,943 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_1) current state - V: None, AS: /Off
2025-07-15 16:12:43,943 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_1)
2025-07-15 16:12:43,943 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,943 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_1): V=/Yes, AS=/Yes
2025-07-15 16:12:43,946 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_1) new state - V: /Yes, AS: /Yes
2025-07-15 16:12:43,946 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_1) to True
2025-07-15 16:12:43,946 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_1)' = True
2025-07-15 16:12:43,950 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: inspections.as_is_condition -> '(Checkbox_1)' = True
2025-07-15 16:12:43,950 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_2)
2025-07-15 16:12:43,951 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_2) has appearance dictionary
2025-07-15 16:12:43,951 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_2) normal appearance keys: ['/Checkbox_2', '/Off']
2025-07-15 16:12:43,952 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_2) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,952 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_2): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:43,953 - __main__ - DEBUG - _handle_button_field:154 - Converted input value False to boolean: False
2025-07-15 16:12:43,953 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_2) current state - V: None, AS: /Off
2025-07-15 16:12:43,953 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_2)
2025-07-15 16:12:43,954 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_2): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,954 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_2): V=/Off, AS=/Off
2025-07-15 16:12:43,955 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_2) new state - V: /Off, AS: /Off
2025-07-15 16:12:43,955 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_2) to False
2025-07-15 16:12:43,955 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_2)' = False
2025-07-15 16:12:43,959 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: inspections.seller_repairs -> '(Checkbox_2)' = False
2025-07-15 16:12:43,960 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_3)
2025-07-15 16:12:43,960 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_3) has appearance dictionary
2025-07-15 16:12:43,960 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_3) normal appearance keys: ['/Checkbox_3', '/Off']
2025-07-15 16:12:43,960 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_3) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,960 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_3): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:43,961 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:12:43,961 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_3) current state - V: None, AS: /Off
2025-07-15 16:12:43,961 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_3)
2025-07-15 16:12:43,961 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,961 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_3): V=/Yes, AS=/Yes
2025-07-15 16:12:43,962 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_3) new state - V: /Yes, AS: /Yes
2025-07-15 16:12:43,962 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_3) to True
2025-07-15 16:12:43,962 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_3)' = True
2025-07-15 16:12:43,964 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: inspections.buyer_inspection -> '(Checkbox_3)' = True
2025-07-15 16:12:43,965 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_4)
2025-07-15 16:12:43,965 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_4) has appearance dictionary
2025-07-15 16:12:43,966 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_4) normal appearance keys: ['/Checkbox_4', '/Off']
2025-07-15 16:12:43,966 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_4) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,966 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_4): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:43,966 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:12:43,966 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_4) current state - V: None, AS: /Off
2025-07-15 16:12:43,966 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_4)
2025-07-15 16:12:43,966 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,967 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_4): V=/Yes, AS=/Yes
2025-07-15 16:12:43,967 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_4) new state - V: /Yes, AS: /Yes
2025-07-15 16:12:43,967 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_4) to True
2025-07-15 16:12:43,967 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_4)' = True
2025-07-15 16:12:43,969 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: inspections.professional_inspection -> '(Checkbox_4)' = True
2025-07-15 16:12:43,970 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_5)
2025-07-15 16:12:43,971 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_5) has appearance dictionary
2025-07-15 16:12:43,974 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_5) normal appearance keys: ['/Checkbox_5', '/Off']
2025-07-15 16:12:43,974 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_5) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,974 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_5): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:43,974 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:12:43,974 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_5) current state - V: None, AS: /Off
2025-07-15 16:12:43,975 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_5)
2025-07-15 16:12:43,975 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_5): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,975 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_5): V=/Yes, AS=/Yes
2025-07-15 16:12:43,975 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_5) new state - V: /Yes, AS: /Yes
2025-07-15 16:12:43,975 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_5) to True
2025-07-15 16:12:43,975 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_5)' = True
2025-07-15 16:12:43,977 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: inspections.termite_inspection -> '(Checkbox_5)' = True
2025-07-15 16:12:43,977 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_6)
2025-07-15 16:12:43,978 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_6) has appearance dictionary
2025-07-15 16:12:43,978 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_6) normal appearance keys: ['/Checkbox_6', '/Off']
2025-07-15 16:12:43,978 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_6) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:43,978 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_6): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:43,979 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:12:43,979 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_6) current state - V: None, AS: /Off
2025-07-15 16:12:43,980 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_6)
2025-07-15 16:12:43,980 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_6): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:43,980 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_6): V=/Yes, AS=/Yes
2025-07-15 16:12:43,980 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_6) new state - V: /Yes, AS: /Yes
2025-07-15 16:12:43,980 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_6) to True
2025-07-15 16:12:43,981 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_6)' = True
2025-07-15 16:12:43,982 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: inspections.roof_inspection -> '(Checkbox_6)' = True
2025-07-15 16:12:43,983 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_28)
2025-07-15 16:12:43,983 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_28) has appearance dictionary
2025-07-15 16:12:43,984 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_28) normal appearance keys: ['/Checkbox_28', '/Off']
2025-07-15 16:12:43,984 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_28) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:44,006 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_28): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:44,007 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:12:44,007 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_28) current state - V: None, AS: /Off
2025-07-15 16:12:44,007 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_28)
2025-07-15 16:12:44,007 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_28): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:44,007 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_28): V=/Yes, AS=/Yes
2025-07-15 16:12:44,007 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_28) new state - V: /Yes, AS: /Yes
2025-07-15 16:12:44,007 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_28) to True
2025-07-15 16:12:44,007 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_28)' = True
2025-07-15 16:12:44,008 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: appliances.refrigerator -> '(Checkbox_28)' = True
2025-07-15 16:12:44,009 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_29)
2025-07-15 16:12:44,009 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_29) has appearance dictionary
2025-07-15 16:12:44,010 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_29) normal appearance keys: ['/Checkbox_29', '/Off']
2025-07-15 16:12:44,010 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_29) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:44,010 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_29): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:44,010 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:12:44,010 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_29) current state - V: None, AS: /Off
2025-07-15 16:12:44,011 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_29)
2025-07-15 16:12:44,011 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_29): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:44,011 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_29): V=/Yes, AS=/Yes
2025-07-15 16:12:44,012 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_29) new state - V: /Yes, AS: /Yes
2025-07-15 16:12:44,013 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_29) to True
2025-07-15 16:12:44,013 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_29)' = True
2025-07-15 16:12:44,014 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: appliances.washer -> '(Checkbox_29)' = True
2025-07-15 16:12:44,015 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_30)
2025-07-15 16:12:44,015 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_30) has appearance dictionary
2025-07-15 16:12:44,016 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_30) normal appearance keys: ['/Checkbox_30', '/Off']
2025-07-15 16:12:44,016 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_30) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:44,016 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_30): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:44,016 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:12:44,016 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_30) current state - V: None, AS: /Off
2025-07-15 16:12:44,016 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_30)
2025-07-15 16:12:44,017 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_30): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:44,017 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_30): V=/Yes, AS=/Yes
2025-07-15 16:12:44,017 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_30) new state - V: /Yes, AS: /Yes
2025-07-15 16:12:44,017 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_30) to True
2025-07-15 16:12:44,017 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_30)' = True
2025-07-15 16:12:44,023 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: appliances.dryer -> '(Checkbox_30)' = True
2025-07-15 16:12:44,024 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_31)
2025-07-15 16:12:44,024 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_31) has appearance dictionary
2025-07-15 16:12:44,024 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_31) normal appearance keys: ['/Checkbox_31', '/Off']
2025-07-15 16:12:44,024 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_31) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:44,025 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_31): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:44,025 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:12:44,025 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_31) current state - V: None, AS: /Off
2025-07-15 16:12:44,025 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_31)
2025-07-15 16:12:44,025 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_31): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:44,025 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_31): V=/Yes, AS=/Yes
2025-07-15 16:12:44,025 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_31) new state - V: /Yes, AS: /Yes
2025-07-15 16:12:44,025 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_31) to True
2025-07-15 16:12:44,026 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_31)' = True
2025-07-15 16:12:44,027 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: appliances.dishwasher -> '(Checkbox_31)' = True
2025-07-15 16:12:44,027 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_32)
2025-07-15 16:12:44,028 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_32) has appearance dictionary
2025-07-15 16:12:44,028 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_32) normal appearance keys: ['/Checkbox_32', '/Off']
2025-07-15 16:12:44,028 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_32) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:44,028 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_32): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:44,029 - __main__ - DEBUG - _handle_button_field:154 - Converted input value False to boolean: False
2025-07-15 16:12:44,029 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_32) current state - V: None, AS: /Off
2025-07-15 16:12:44,030 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_32)
2025-07-15 16:12:44,030 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_32): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:44,030 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_32): V=/Off, AS=/Off
2025-07-15 16:12:44,030 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_32) new state - V: /Off, AS: /Off
2025-07-15 16:12:44,030 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_32) to False
2025-07-15 16:12:44,030 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_32)' = False
2025-07-15 16:12:44,032 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: appliances.microwave -> '(Checkbox_32)' = False
2025-07-15 16:12:44,032 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_33)
2025-07-15 16:12:44,032 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_33) has appearance dictionary
2025-07-15 16:12:44,033 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_33) normal appearance keys: ['/Checkbox_33', '/Off']
2025-07-15 16:12:44,033 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_33) assumed to be checkbox based on name pattern: check
2025-07-15 16:12:44,033 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_33): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:12:44,033 - __main__ - DEBUG - _handle_button_field:154 - Converted input value False to boolean: False
2025-07-15 16:12:44,033 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_33) current state - V: None, AS: /Off
2025-07-15 16:12:44,033 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_33)
2025-07-15 16:12:44,033 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_33): Dict key '/Yes' is not a PdfName
2025-07-15 16:12:44,033 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_33): V=/Off, AS=/Off
2025-07-15 16:12:44,033 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_33) new state - V: /Off, AS: /Off
2025-07-15 16:12:44,033 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_33) to False
2025-07-15 16:12:44,034 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_33)' = False
2025-07-15 16:12:44,035 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: appliances.oven_range -> '(Checkbox_33)' = False
2025-07-15 16:12:44,128 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 21, Failed: 0
2025-07-15 16:12:44,128 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 21, Failed: 0
2025-07-15 16:12:44,129 - __main__ - INFO - fill_pdf:1151 - Checkbox debug mode disabled
2025-07-15 16:12:44,129 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:12:44] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:52:15,753 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:52:15] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:52:15,759 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 5 fields
2025-07-15 16:52:15,875 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:52:15,881 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:15,882 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:15,882 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:52:15,884 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:15,884 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:15,884 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:52:15,886 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:15,886 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:15,887 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:52:15,890 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:15,890 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:15,890 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:52:15,892 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_1) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:15,892 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:15,892 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_1) to True
2025-07-15 16:52:15,896 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_3) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:15,896 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:15,896 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_3) to True
2025-07-15 16:52:15,898 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_4) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:15,898 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:15,898 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_4) to False
2025-07-15 16:52:15,967 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 10, Failed: 0
2025-07-15 16:52:15,967 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 10, Failed: 0
2025-07-15 16:52:15,968 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:52:15] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:52:16,008 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:52:16] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:52:16,018 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 5 fields
2025-07-15 16:52:16,072 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:52:16,077 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,077 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,078 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:52:16,080 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,080 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,081 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:52:16,082 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,082 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,083 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:52:16,086 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,087 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,087 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:52:16,088 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_1) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,089 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,089 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_1) to True
2025-07-15 16:52:16,090 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_3) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,090 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,091 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_3) to True
2025-07-15 16:52:16,093 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_4) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,094 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,094 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_4) to False
2025-07-15 16:52:16,171 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 10, Failed: 0
2025-07-15 16:52:16,171 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 10, Failed: 0
2025-07-15 16:52:16,171 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:52:16] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:52:16,198 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:52:16] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:52:16,203 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 5 fields
2025-07-15 16:52:16,259 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:52:16,264 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,264 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,265 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:52:16,267 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,267 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,267 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:52:16,269 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,269 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,269 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:52:16,272 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,273 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,273 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:52:16,276 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_1) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,276 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,277 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_1) to True
2025-07-15 16:52:16,279 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_3) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,280 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,280 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_3) to True
2025-07-15 16:52:16,282 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_4) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,282 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,282 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_4) to False
2025-07-15 16:52:16,347 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 10, Failed: 0
2025-07-15 16:52:16,347 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 10, Failed: 0
2025-07-15 16:52:16,348 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:52:16] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:52:16,373 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:52:16] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:52:16,389 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 6 fields
2025-07-15 16:52:16,509 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:52:16,515 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,515 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,518 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:52:16,520 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,520 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,520 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:52:16,523 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,523 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,523 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:52:16,525 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,526 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,526 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:52:16,528 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_1) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,528 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,528 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_1) to True
2025-07-15 16:52:16,531 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_3) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,531 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,531 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_3) to True
2025-07-15 16:52:16,534 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_4) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,534 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,534 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_4) to False
2025-07-15 16:52:16,537 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_28) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,537 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_28): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,538 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_28) to True
2025-07-15 16:52:16,541 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_29) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,541 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_29): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,541 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_29) to True
2025-07-15 16:52:16,544 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_30) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,544 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_30): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,544 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_30) to True
2025-07-15 16:52:16,547 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_31) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,548 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_31): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,548 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_31) to True
2025-07-15 16:52:16,550 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_32) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,550 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_32): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,550 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_32) to False
2025-07-15 16:52:16,554 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_33) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,554 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_33): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,554 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_33) to False
2025-07-15 16:52:16,632 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 16, Failed: 0
2025-07-15 16:52:16,632 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 16, Failed: 0
2025-07-15 16:52:16,633 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:52:16] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:52:16,651 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:52:16] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:52:16,655 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:52:16] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:52:16,659 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:52:16] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 16:52:16,669 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 6 fields
2025-07-15 16:52:16,669 - __main__ - INFO - fill_pdf:1102 - Checkbox debug mode enabled
2025-07-15 16:52:16,726 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:52:16,726 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Text_1)' = Debug Test Seller
2025-07-15 16:52:16,728 - __main__ - DEBUG - _fill_regular_field:981 - Successfully filled field: seller_name -> '(Text_1)' = Debug Test Seller
2025-07-15 16:52:16,729 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Text_2)' = Debug Test Buyer
2025-07-15 16:52:16,730 - __main__ - DEBUG - _fill_regular_field:981 - Successfully filled field: buyer_name -> '(Text_2)' = Debug Test Buyer
2025-07-15 16:52:16,731 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Number_1)' = 750000
2025-07-15 16:52:16,732 - __main__ - DEBUG - _fill_regular_field:981 - Successfully filled field: purchase_price -> '(Number_1)' = 750000
2025-07-15 16:52:16,732 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_8)
2025-07-15 16:52:16,733 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_8) has appearance dictionary
2025-07-15 16:52:16,733 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_8) normal appearance keys: ['/Checkbox_8', '/Off']
2025-07-15 16:52:16,733 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,733 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_8): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,733 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:52:16,733 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_8) current state - V: None, AS: /Off
2025-07-15 16:52:16,733 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_8)
2025-07-15 16:52:16,733 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,734 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_8): V=/Yes, AS=/Yes
2025-07-15 16:52:16,734 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_8) new state - V: /Yes, AS: /Yes
2025-07-15 16:52:16,734 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:52:16,734 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_8)' = True
2025-07-15 16:52:16,735 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: financing.cash -> '(Checkbox_8)' = True
2025-07-15 16:52:16,736 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_9)
2025-07-15 16:52:16,736 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_9) has appearance dictionary
2025-07-15 16:52:16,736 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_9) normal appearance keys: ['/Checkbox_9', '/Off']
2025-07-15 16:52:16,736 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,736 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_9): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,736 - __main__ - DEBUG - _handle_button_field:154 - Converted input value False to boolean: False
2025-07-15 16:52:16,736 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_9) current state - V: None, AS: /Off
2025-07-15 16:52:16,736 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_9)
2025-07-15 16:52:16,736 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,736 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_9): V=/Off, AS=/Off
2025-07-15 16:52:16,737 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_9) new state - V: /Off, AS: /Off
2025-07-15 16:52:16,737 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:52:16,737 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_9)' = False
2025-07-15 16:52:16,738 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: financing.conventional -> '(Checkbox_9)' = False
2025-07-15 16:52:16,739 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_10)
2025-07-15 16:52:16,740 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_10) has appearance dictionary
2025-07-15 16:52:16,740 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_10) normal appearance keys: ['/Checkbox_10', '/Off']
2025-07-15 16:52:16,741 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,741 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_10): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,741 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:52:16,741 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_10) current state - V: None, AS: /Off
2025-07-15 16:52:16,741 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_10)
2025-07-15 16:52:16,741 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,741 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_10): V=/Yes, AS=/Yes
2025-07-15 16:52:16,741 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_10) new state - V: /Yes, AS: /Yes
2025-07-15 16:52:16,741 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:52:16,742 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_10)' = True
2025-07-15 16:52:16,743 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: financing.fha -> '(Checkbox_10)' = True
2025-07-15 16:52:16,744 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_11)
2025-07-15 16:52:16,745 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_11) has appearance dictionary
2025-07-15 16:52:16,745 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_11) normal appearance keys: ['/Checkbox_11', '/Off']
2025-07-15 16:52:16,745 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,746 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_11): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,746 - __main__ - DEBUG - _handle_button_field:154 - Converted input value False to boolean: False
2025-07-15 16:52:16,746 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_11) current state - V: None, AS: /Off
2025-07-15 16:52:16,746 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_11)
2025-07-15 16:52:16,746 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,746 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_11): V=/Off, AS=/Off
2025-07-15 16:52:16,746 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_11) new state - V: /Off, AS: /Off
2025-07-15 16:52:16,747 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:52:16,747 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_11)' = False
2025-07-15 16:52:16,748 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: financing.va -> '(Checkbox_11)' = False
2025-07-15 16:52:16,749 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_12)
2025-07-15 16:52:16,749 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_12) has appearance dictionary
2025-07-15 16:52:16,750 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_12) normal appearance keys: ['/Checkbox_12', '/Off']
2025-07-15 16:52:16,750 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_12) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,750 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_12): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,750 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:52:16,751 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_12) current state - V: None, AS: /Off
2025-07-15 16:52:16,751 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_12)
2025-07-15 16:52:16,751 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_12): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,751 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_12): V=/Yes, AS=/Yes
2025-07-15 16:52:16,751 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_12) new state - V: /Yes, AS: /Yes
2025-07-15 16:52:16,751 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_12) to True
2025-07-15 16:52:16,752 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_12)' = True
2025-07-15 16:52:16,753 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: financing.usda -> '(Checkbox_12)' = True
2025-07-15 16:52:16,754 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_13)
2025-07-15 16:52:16,754 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_13) has appearance dictionary
2025-07-15 16:52:16,755 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_13) normal appearance keys: ['/Checkbox_13', '/Off']
2025-07-15 16:52:16,755 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_13) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,755 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_13): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,755 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:52:16,755 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_13) current state - V: None, AS: /Off
2025-07-15 16:52:16,755 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_13)
2025-07-15 16:52:16,758 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_13): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,758 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_13): V=/Yes, AS=/Yes
2025-07-15 16:52:16,758 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_13) new state - V: /Yes, AS: /Yes
2025-07-15 16:52:16,758 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_13) to True
2025-07-15 16:52:16,758 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_13)' = True
2025-07-15 16:52:16,761 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: financing.other -> '(Checkbox_13)' = True
2025-07-15 16:52:16,761 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_1)
2025-07-15 16:52:16,762 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_1) has appearance dictionary
2025-07-15 16:52:16,762 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_1) normal appearance keys: ['/Checkbox_1', '/Off']
2025-07-15 16:52:16,764 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_1) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,764 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_1): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,765 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:52:16,765 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_1) current state - V: None, AS: /Off
2025-07-15 16:52:16,765 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_1)
2025-07-15 16:52:16,765 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,766 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_1): V=/Yes, AS=/Yes
2025-07-15 16:52:16,766 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_1) new state - V: /Yes, AS: /Yes
2025-07-15 16:52:16,766 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_1) to True
2025-07-15 16:52:16,766 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_1)' = True
2025-07-15 16:52:16,769 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: inspections.as_is_condition -> '(Checkbox_1)' = True
2025-07-15 16:52:16,771 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_2)
2025-07-15 16:52:16,772 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_2) has appearance dictionary
2025-07-15 16:52:16,773 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_2) normal appearance keys: ['/Checkbox_2', '/Off']
2025-07-15 16:52:16,773 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_2) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,774 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_2): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,774 - __main__ - DEBUG - _handle_button_field:154 - Converted input value False to boolean: False
2025-07-15 16:52:16,774 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_2) current state - V: None, AS: /Off
2025-07-15 16:52:16,774 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_2)
2025-07-15 16:52:16,774 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_2): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,774 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_2): V=/Off, AS=/Off
2025-07-15 16:52:16,774 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_2) new state - V: /Off, AS: /Off
2025-07-15 16:52:16,774 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_2) to False
2025-07-15 16:52:16,774 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_2)' = False
2025-07-15 16:52:16,778 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: inspections.seller_repairs -> '(Checkbox_2)' = False
2025-07-15 16:52:16,779 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_3)
2025-07-15 16:52:16,779 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_3) has appearance dictionary
2025-07-15 16:52:16,785 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_3) normal appearance keys: ['/Checkbox_3', '/Off']
2025-07-15 16:52:16,786 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_3) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,786 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_3): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,786 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:52:16,786 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_3) current state - V: None, AS: /Off
2025-07-15 16:52:16,786 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_3)
2025-07-15 16:52:16,786 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,790 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_3): V=/Yes, AS=/Yes
2025-07-15 16:52:16,791 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_3) new state - V: /Yes, AS: /Yes
2025-07-15 16:52:16,792 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_3) to True
2025-07-15 16:52:16,792 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_3)' = True
2025-07-15 16:52:16,795 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: inspections.buyer_inspection -> '(Checkbox_3)' = True
2025-07-15 16:52:16,796 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_4)
2025-07-15 16:52:16,796 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_4) has appearance dictionary
2025-07-15 16:52:16,796 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_4) normal appearance keys: ['/Checkbox_4', '/Off']
2025-07-15 16:52:16,796 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_4) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,796 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_4): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,796 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:52:16,797 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_4) current state - V: None, AS: /Off
2025-07-15 16:52:16,797 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_4)
2025-07-15 16:52:16,797 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,797 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_4): V=/Yes, AS=/Yes
2025-07-15 16:52:16,797 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_4) new state - V: /Yes, AS: /Yes
2025-07-15 16:52:16,797 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_4) to True
2025-07-15 16:52:16,798 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_4)' = True
2025-07-15 16:52:16,803 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: inspections.professional_inspection -> '(Checkbox_4)' = True
2025-07-15 16:52:16,803 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_5)
2025-07-15 16:52:16,803 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_5) has appearance dictionary
2025-07-15 16:52:16,804 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_5) normal appearance keys: ['/Checkbox_5', '/Off']
2025-07-15 16:52:16,804 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_5) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,804 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_5): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,804 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:52:16,804 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_5) current state - V: None, AS: /Off
2025-07-15 16:52:16,805 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_5)
2025-07-15 16:52:16,805 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_5): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,805 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_5): V=/Yes, AS=/Yes
2025-07-15 16:52:16,805 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_5) new state - V: /Yes, AS: /Yes
2025-07-15 16:52:16,805 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_5) to True
2025-07-15 16:52:16,805 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_5)' = True
2025-07-15 16:52:16,807 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: inspections.termite_inspection -> '(Checkbox_5)' = True
2025-07-15 16:52:16,808 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_6)
2025-07-15 16:52:16,809 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_6) has appearance dictionary
2025-07-15 16:52:16,809 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_6) normal appearance keys: ['/Checkbox_6', '/Off']
2025-07-15 16:52:16,809 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_6) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,810 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_6): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,810 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:52:16,810 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_6) current state - V: None, AS: /Off
2025-07-15 16:52:16,810 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_6)
2025-07-15 16:52:16,810 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_6): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,810 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_6): V=/Yes, AS=/Yes
2025-07-15 16:52:16,811 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_6) new state - V: /Yes, AS: /Yes
2025-07-15 16:52:16,811 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_6) to True
2025-07-15 16:52:16,811 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_6)' = True
2025-07-15 16:52:16,813 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: inspections.roof_inspection -> '(Checkbox_6)' = True
2025-07-15 16:52:16,813 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_28)
2025-07-15 16:52:16,815 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_28) has appearance dictionary
2025-07-15 16:52:16,815 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_28) normal appearance keys: ['/Checkbox_28', '/Off']
2025-07-15 16:52:16,816 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_28) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,816 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_28): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,816 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:52:16,816 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_28) current state - V: None, AS: /Off
2025-07-15 16:52:16,816 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_28)
2025-07-15 16:52:16,817 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_28): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,817 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_28): V=/Yes, AS=/Yes
2025-07-15 16:52:16,817 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_28) new state - V: /Yes, AS: /Yes
2025-07-15 16:52:16,817 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_28) to True
2025-07-15 16:52:16,817 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_28)' = True
2025-07-15 16:52:16,819 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: appliances.refrigerator -> '(Checkbox_28)' = True
2025-07-15 16:52:16,819 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_29)
2025-07-15 16:52:16,820 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_29) has appearance dictionary
2025-07-15 16:52:16,821 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_29) normal appearance keys: ['/Checkbox_29', '/Off']
2025-07-15 16:52:16,821 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_29) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,821 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_29): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,821 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:52:16,822 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_29) current state - V: None, AS: /Off
2025-07-15 16:52:16,822 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_29)
2025-07-15 16:52:16,822 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_29): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,822 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_29): V=/Yes, AS=/Yes
2025-07-15 16:52:16,822 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_29) new state - V: /Yes, AS: /Yes
2025-07-15 16:52:16,822 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_29) to True
2025-07-15 16:52:16,823 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_29)' = True
2025-07-15 16:52:16,825 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: appliances.washer -> '(Checkbox_29)' = True
2025-07-15 16:52:16,826 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_30)
2025-07-15 16:52:16,826 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_30) has appearance dictionary
2025-07-15 16:52:16,827 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_30) normal appearance keys: ['/Checkbox_30', '/Off']
2025-07-15 16:52:16,828 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_30) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,828 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_30): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,828 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:52:16,828 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_30) current state - V: None, AS: /Off
2025-07-15 16:52:16,829 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_30)
2025-07-15 16:52:16,829 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_30): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,829 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_30): V=/Yes, AS=/Yes
2025-07-15 16:52:16,829 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_30) new state - V: /Yes, AS: /Yes
2025-07-15 16:52:16,829 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_30) to True
2025-07-15 16:52:16,830 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_30)' = True
2025-07-15 16:52:16,832 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: appliances.dryer -> '(Checkbox_30)' = True
2025-07-15 16:52:16,834 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_31)
2025-07-15 16:52:16,834 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_31) has appearance dictionary
2025-07-15 16:52:16,835 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_31) normal appearance keys: ['/Checkbox_31', '/Off']
2025-07-15 16:52:16,835 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_31) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,836 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_31): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,836 - __main__ - DEBUG - _handle_button_field:154 - Converted input value True to boolean: True
2025-07-15 16:52:16,836 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_31) current state - V: None, AS: /Off
2025-07-15 16:52:16,836 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_31)
2025-07-15 16:52:16,837 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_31): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,837 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_31): V=/Yes, AS=/Yes
2025-07-15 16:52:16,837 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_31) new state - V: /Yes, AS: /Yes
2025-07-15 16:52:16,837 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_31) to True
2025-07-15 16:52:16,837 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_31)' = True
2025-07-15 16:52:16,840 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: appliances.dishwasher -> '(Checkbox_31)' = True
2025-07-15 16:52:16,841 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_32)
2025-07-15 16:52:16,841 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_32) has appearance dictionary
2025-07-15 16:52:16,842 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_32) normal appearance keys: ['/Checkbox_32', '/Off']
2025-07-15 16:52:16,842 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_32) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,842 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_32): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,842 - __main__ - DEBUG - _handle_button_field:154 - Converted input value False to boolean: False
2025-07-15 16:52:16,843 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_32) current state - V: None, AS: /Off
2025-07-15 16:52:16,843 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_32)
2025-07-15 16:52:16,843 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_32): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,843 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_32): V=/Off, AS=/Off
2025-07-15 16:52:16,844 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_32) new state - V: /Off, AS: /Off
2025-07-15 16:52:16,844 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_32) to False
2025-07-15 16:52:16,844 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_32)' = False
2025-07-15 16:52:16,846 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: appliances.microwave -> '(Checkbox_32)' = False
2025-07-15 16:52:16,849 - __main__ - DEBUG - _handle_button_field:143 - Processing button field: (Checkbox_33)
2025-07-15 16:52:16,849 - __main__ - DEBUG - _analyze_checkbox_field:194 - Field (Checkbox_33) has appearance dictionary
2025-07-15 16:52:16,850 - __main__ - DEBUG - _analyze_checkbox_field:199 - Field (Checkbox_33) normal appearance keys: ['/Checkbox_33', '/Off']
2025-07-15 16:52:16,850 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_33) assumed to be checkbox based on name pattern: check
2025-07-15 16:52:16,850 - __main__ - DEBUG - _analyze_checkbox_field:267 - Checkbox analysis for (Checkbox_33): {'is_checkbox': True, 'on_state': '/Yes', 'off_state': '/Off', 'has_appearance_dict': True, 'detection_method': 'name_pattern_check'}
2025-07-15 16:52:16,850 - __main__ - DEBUG - _handle_button_field:154 - Converted input value False to boolean: False
2025-07-15 16:52:16,850 - __main__ - DEBUG - _handle_button_field:159 - Field (Checkbox_33) current state - V: None, AS: /Off
2025-07-15 16:52:16,851 - __main__ - DEBUG - _ensure_checkbox_appearance_dict:344 - Adding /Yes appearance state for (Checkbox_33)
2025-07-15 16:52:16,851 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_33): Dict key '/Yes' is not a PdfName
2025-07-15 16:52:16,851 - __main__ - DEBUG - _set_checkbox_value:314 - Successfully set checkbox (Checkbox_33): V=/Off, AS=/Off
2025-07-15 16:52:16,851 - __main__ - DEBUG - _handle_button_field:167 - Field (Checkbox_33) new state - V: /Off, AS: /Off
2025-07-15 16:52:16,851 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_33) to False
2025-07-15 16:52:16,851 - __main__ - DEBUG - set_pdf_field:481 - Successfully set field '(Checkbox_33)' = False
2025-07-15 16:52:16,854 - __main__ - DEBUG - _fill_checkbox_group:1012 - Successfully filled checkbox: appliances.oven_range -> '(Checkbox_33)' = False
2025-07-15 16:52:16,918 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 21, Failed: 0
2025-07-15 16:52:16,918 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 21, Failed: 0
2025-07-15 16:52:16,918 - __main__ - INFO - fill_pdf:1151 - Checkbox debug mode disabled
2025-07-15 16:52:16,919 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:52:16] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:56:10,533 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/test_checkbox_enhancement.py', reloading
2025-07-15 16:56:10,640 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 16:56:11,305 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 16:56:11,307 - __main__ - INFO - _load_field_mapping:564 - Loaded field mapping with 115 fields
2025-07-15 16:56:11,307 - __main__ - WARNING - _load_field_mapping_guide:582 - Field mapping guide not found
2025-07-15 16:56:11,308 - __main__ - INFO - _load_pdf_fields_info:594 - Loaded 188 PDF field definitions
2025-07-15 16:56:11,334 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 16:56:11,372 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 16:56:29,570 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/test_checkbox_enhancement.py', reloading
2025-07-15 16:56:29,651 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 16:56:29,949 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 16:56:29,949 - __main__ - INFO - _load_field_mapping:564 - Loaded field mapping with 115 fields
2025-07-15 16:56:29,949 - __main__ - WARNING - _load_field_mapping_guide:582 - Field mapping guide not found
2025-07-15 16:56:29,950 - __main__ - INFO - _load_pdf_fields_info:594 - Loaded 188 PDF field definitions
2025-07-15 16:56:29,964 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 16:56:29,974 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 16:56:39,668 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 5 fields
2025-07-15 16:56:39,749 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:56:39,754 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:39,754 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:39,754 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:56:39,757 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:39,757 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:39,757 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:56:39,762 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:39,763 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:39,763 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:56:39,766 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:39,766 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:39,766 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:56:39,769 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_1) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:39,769 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:39,769 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_1) to True
2025-07-15 16:56:39,771 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_3) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:39,771 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:39,771 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_3) to False
2025-07-15 16:56:39,773 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_4) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:39,773 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:39,773 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_4) to True
2025-07-15 16:56:39,848 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 10, Failed: 0
2025-07-15 16:56:39,848 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 10, Failed: 0
2025-07-15 16:56:39,849 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:56:39] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:56:39,915 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 5 fields
2025-07-15 16:56:39,980 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:56:39,985 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:39,986 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:39,986 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:56:39,989 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:39,989 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:39,989 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:56:39,991 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:39,994 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:39,994 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:56:39,996 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:39,996 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:39,996 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:56:39,998 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_28) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:39,999 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_28): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:39,999 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_28) to True
2025-07-15 16:56:40,002 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_29) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:40,002 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_29): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:40,002 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_29) to True
2025-07-15 16:56:40,005 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_30) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:40,005 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_30): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:40,005 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_30) to True
2025-07-15 16:56:40,007 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_31) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:40,008 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_31): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:40,008 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_31) to False
2025-07-15 16:56:40,078 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 11, Failed: 0
2025-07-15 16:56:40,078 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 11, Failed: 0
2025-07-15 16:56:40,079 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:56:40] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:56:40,147 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 4 fields
2025-07-15 16:56:40,330 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:56:40,349 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:40,349 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:40,349 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:56:40,351 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:40,351 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:40,351 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:56:40,354 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:40,355 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:40,356 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to False
2025-07-15 16:56:40,362 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:56:40,362 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:56:40,362 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to True
2025-07-15 16:56:40,431 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 7, Failed: 0
2025-07-15 16:56:40,431 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 7, Failed: 0
2025-07-15 16:56:40,431 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:56:40] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:57:23,570 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/debug_pdf_fields.py', reloading
2025-07-15 16:57:23,694 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 16:57:24,021 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 16:57:24,022 - __main__ - INFO - _load_field_mapping:564 - Loaded field mapping with 115 fields
2025-07-15 16:57:24,022 - __main__ - WARNING - _load_field_mapping_guide:582 - Field mapping guide not found
2025-07-15 16:57:24,022 - __main__ - INFO - _load_pdf_fields_info:594 - Loaded 188 PDF field definitions
2025-07-15 16:57:24,040 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 16:57:24,053 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 16:57:47,495 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/checkbox_verification.py', reloading
2025-07-15 16:57:47,553 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 16:57:47,887 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 16:57:47,887 - __main__ - INFO - _load_field_mapping:564 - Loaded field mapping with 115 fields
2025-07-15 16:57:47,888 - __main__ - WARNING - _load_field_mapping_guide:582 - Field mapping guide not found
2025-07-15 16:57:47,888 - __main__ - INFO - _load_pdf_fields_info:594 - Loaded 188 PDF field definitions
2025-07-15 16:57:47,919 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 16:57:47,938 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 16:58:00,187 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/test_checkbox_enhancement.py', reloading
2025-07-15 16:58:00,264 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 16:58:01,228 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 16:58:01,229 - __main__ - INFO - _load_field_mapping:564 - Loaded field mapping with 115 fields
2025-07-15 16:58:01,230 - __main__ - WARNING - _load_field_mapping_guide:582 - Field mapping guide not found
2025-07-15 16:58:01,230 - __main__ - INFO - _load_pdf_fields_info:594 - Loaded 188 PDF field definitions
2025-07-15 16:58:01,257 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 16:58:01,271 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 16:58:12,001 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 5 fields
2025-07-15 16:58:12,076 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:58:12,081 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,082 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,082 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:58:12,086 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,086 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,086 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:58:12,089 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,091 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,091 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:58:12,093 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,093 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,094 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:58:12,097 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_1) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,097 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,097 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_1) to True
2025-07-15 16:58:12,099 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_3) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,099 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,100 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_3) to False
2025-07-15 16:58:12,101 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_4) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,101 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,102 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_4) to True
2025-07-15 16:58:12,172 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 10, Failed: 0
2025-07-15 16:58:12,172 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 10, Failed: 0
2025-07-15 16:58:12,173 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:58:12] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:58:12,241 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 5 fields
2025-07-15 16:58:12,299 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:58:12,311 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,312 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,312 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:58:12,315 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,315 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,316 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:58:12,319 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,319 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,319 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:58:12,322 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,324 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,324 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:58:12,327 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_28) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,327 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_28): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,328 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_28) to True
2025-07-15 16:58:12,331 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_29) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,331 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_29): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,332 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_29) to True
2025-07-15 16:58:12,334 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_30) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,334 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_30): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,335 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_30) to True
2025-07-15 16:58:12,338 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_31) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,338 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_31): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,338 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_31) to False
2025-07-15 16:58:12,422 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 11, Failed: 0
2025-07-15 16:58:12,424 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 11, Failed: 0
2025-07-15 16:58:12,425 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:58:12] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:58:12,521 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 4 fields
2025-07-15 16:58:12,604 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:58:12,609 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,609 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,612 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:58:12,615 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,615 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,615 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:58:12,618 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,619 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,619 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to False
2025-07-15 16:58:12,620 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:12,620 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:12,621 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to True
2025-07-15 16:58:12,692 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 7, Failed: 0
2025-07-15 16:58:12,692 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 7, Failed: 0
2025-07-15 16:58:12,692 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:58:12] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:58:38,991 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/checkbox_verification.py', reloading
2025-07-15 16:58:39,304 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 16:58:40,870 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 16:58:40,872 - __main__ - INFO - _load_field_mapping:564 - Loaded field mapping with 115 fields
2025-07-15 16:58:40,872 - __main__ - WARNING - _load_field_mapping_guide:582 - Field mapping guide not found
2025-07-15 16:58:40,873 - __main__ - INFO - _load_pdf_fields_info:594 - Loaded 188 PDF field definitions
2025-07-15 16:58:40,937 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 16:58:40,979 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 16:58:57,992 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 5 fields
2025-07-15 16:58:58,051 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:58:58,057 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,057 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,057 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:58:58,060 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,060 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,060 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:58:58,062 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,062 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,062 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:58:58,065 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,065 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,065 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:58:58,067 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_1) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,067 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,067 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_1) to True
2025-07-15 16:58:58,069 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_3) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,069 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,069 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_3) to False
2025-07-15 16:58:58,072 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_4) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,073 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,073 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_4) to True
2025-07-15 16:58:58,145 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 10, Failed: 0
2025-07-15 16:58:58,145 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 10, Failed: 0
2025-07-15 16:58:58,146 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:58:58] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:58:58,207 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 5 fields
2025-07-15 16:58:58,260 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:58:58,269 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,269 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,269 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:58:58,272 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,272 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,272 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:58:58,274 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,274 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,274 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to True
2025-07-15 16:58:58,276 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,277 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,277 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to False
2025-07-15 16:58:58,279 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_28) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,279 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_28): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,279 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_28) to True
2025-07-15 16:58:58,281 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_29) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,282 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_29): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,282 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_29) to True
2025-07-15 16:58:58,283 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_30) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,284 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_30): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,284 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_30) to True
2025-07-15 16:58:58,285 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_31) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,285 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_31): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,286 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_31) to False
2025-07-15 16:58:58,350 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 11, Failed: 0
2025-07-15 16:58:58,350 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 11, Failed: 0
2025-07-15 16:58:58,351 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:58:58] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 16:58:58,408 - __main__ - INFO - fill_pdf:1097 - Received PDF fill request with 4 fields
2025-07-15 16:58:58,467 - __main__ - INFO - fill_pdf:915 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 16:58:58,476 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_8) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,476 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,476 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_8) to True
2025-07-15 16:58:58,481 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_9) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,481 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,482 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_9) to False
2025-07-15 16:58:58,484 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_10) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,485 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,485 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_10) to False
2025-07-15 16:58:58,489 - __main__ - WARNING - _analyze_checkbox_field:254 - Field (Checkbox_11) assumed to be checkbox based on name pattern: check
2025-07-15 16:58:58,489 - __main__ - WARNING - _ensure_checkbox_appearance_dict:352 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 16:58:58,489 - __main__ - INFO - _handle_button_field:168 - Successfully set checkbox (Checkbox_11) to True
2025-07-15 16:58:58,569 - __main__ - INFO - fill_pdf:952 - PDF generated successfully. Processed: 7, Failed: 0
2025-07-15 16:58:58,569 - __main__ - INFO - fill_pdf:1115 - PDF generated successfully. Fields processed: 7, Failed: 0
2025-07-15 16:58:58,569 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 16:58:58] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 17:04:30,688 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-15 17:04:30,860 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 17:04:32,239 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 17:04:32,240 - __main__ - INFO - _load_field_mapping:656 - Loaded field mapping with 115 fields
2025-07-15 17:04:32,240 - __main__ - WARNING - _load_field_mapping_guide:674 - Field mapping guide not found
2025-07-15 17:04:32,241 - __main__ - INFO - _load_pdf_fields_info:686 - Loaded 188 PDF field definitions
2025-07-15 17:04:32,389 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 17:04:32,461 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 17:04:50,717 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-15 17:04:50,821 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 17:04:51,413 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 17:04:51,414 - __main__ - INFO - _load_field_mapping:656 - Loaded field mapping with 115 fields
2025-07-15 17:04:51,414 - __main__ - WARNING - _load_field_mapping_guide:674 - Field mapping guide not found
2025-07-15 17:04:51,415 - __main__ - INFO - _load_pdf_fields_info:686 - Loaded 188 PDF field definitions
2025-07-15 17:04:51,434 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 17:04:51,454 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 17:06:00,646 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 17:06:00] "GET /debug-checkboxes HTTP/1.1" 200 -
2025-07-15 17:06:00,656 - __main__ - INFO - fill_pdf:1189 - Received PDF fill request with 6 fields
2025-07-15 17:06:00,656 - __main__ - INFO - fill_pdf:1194 - Checkbox debug mode enabled
2025-07-15 17:06:00,706 - __main__ - INFO - fill_pdf:1007 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 17:06:00,706 - __main__ - DEBUG - set_pdf_field:573 - Successfully set field '(Text_1)' = Enhanced Test Seller
2025-07-15 17:06:00,708 - __main__ - DEBUG - _fill_regular_field:1073 - Successfully filled field: seller_name -> '(Text_1)' = Enhanced Test Seller
2025-07-15 17:06:00,708 - __main__ - DEBUG - set_pdf_field:573 - Successfully set field '(Text_2)' = Enhanced Test Buyer
2025-07-15 17:06:00,709 - __main__ - DEBUG - _fill_regular_field:1073 - Successfully filled field: buyer_name -> '(Text_2)' = Enhanced Test Buyer
2025-07-15 17:06:00,710 - __main__ - DEBUG - set_pdf_field:573 - Successfully set field '(Number_1)' = 850000
2025-07-15 17:06:00,711 - __main__ - DEBUG - _fill_regular_field:1073 - Successfully filled field: purchase_price -> '(Number_1)' = 850000
2025-07-15 17:06:00,711 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_8)
2025-07-15 17:06:00,712 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 17:06:00,712 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_8) appearance keys: ['/Checkbox_8', '/Off']
2025-07-15 17:06:00,712 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,712 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_8) to CHECKED (/Yes)
2025-07-15 17:06:00,713 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_8)
2025-07-15 17:06:00,713 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,713 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,713 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_8)'
2025-07-15 17:06:00,714 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_8)'
2025-07-15 17:06:00,714 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: financing.cash -> '(Checkbox_8)'
2025-07-15 17:06:00,715 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_9)
2025-07-15 17:06:00,715 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 17:06:00,715 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_9) appearance keys: ['/Checkbox_9', '/Off']
2025-07-15 17:06:00,715 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,715 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_9) to UNCHECKED (/Off)
2025-07-15 17:06:00,715 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_9)
2025-07-15 17:06:00,715 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,715 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_9): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,715 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_9)'
2025-07-15 17:06:00,717 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_9)'
2025-07-15 17:06:00,717 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: financing.conventional -> '(Checkbox_9)'
2025-07-15 17:06:00,718 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_10)
2025-07-15 17:06:00,718 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 17:06:00,718 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_10) appearance keys: ['/Checkbox_10', '/Off']
2025-07-15 17:06:00,718 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,718 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_10) to CHECKED (/Yes)
2025-07-15 17:06:00,718 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_10)
2025-07-15 17:06:00,718 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,718 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_10): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,718 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_10)'
2025-07-15 17:06:00,720 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_10)'
2025-07-15 17:06:00,720 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: financing.fha -> '(Checkbox_10)'
2025-07-15 17:06:00,720 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_11)
2025-07-15 17:06:00,721 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 17:06:00,721 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_11) appearance keys: ['/Checkbox_11', '/Off']
2025-07-15 17:06:00,721 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,721 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_11) to UNCHECKED (/Off)
2025-07-15 17:06:00,721 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_11)
2025-07-15 17:06:00,721 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,721 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_11): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,722 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_11)'
2025-07-15 17:06:00,723 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_11)'
2025-07-15 17:06:00,724 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: financing.va -> '(Checkbox_11)'
2025-07-15 17:06:00,724 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_12)
2025-07-15 17:06:00,724 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 17:06:00,724 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_12) appearance keys: ['/Checkbox_12', '/Off']
2025-07-15 17:06:00,724 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,724 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_12) to UNCHECKED (/Off)
2025-07-15 17:06:00,724 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_12)
2025-07-15 17:06:00,725 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_12): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,725 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_12): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,725 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_12)'
2025-07-15 17:06:00,726 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_12)'
2025-07-15 17:06:00,726 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: financing.usda -> '(Checkbox_12)'
2025-07-15 17:06:00,726 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_13)
2025-07-15 17:06:00,727 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 17:06:00,727 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_13) appearance keys: ['/Checkbox_13', '/Off']
2025-07-15 17:06:00,727 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,727 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_13) to UNCHECKED (/Off)
2025-07-15 17:06:00,727 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_13)
2025-07-15 17:06:00,728 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_13): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,728 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_13): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,728 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_13)'
2025-07-15 17:06:00,729 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_13)'
2025-07-15 17:06:00,729 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: financing.other -> '(Checkbox_13)'
2025-07-15 17:06:00,729 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_1)
2025-07-15 17:06:00,729 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 17:06:00,730 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_1) appearance keys: ['/Checkbox_1', '/Off']
2025-07-15 17:06:00,730 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,730 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_1) to CHECKED (/Yes)
2025-07-15 17:06:00,730 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_1)
2025-07-15 17:06:00,730 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,730 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_1): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,730 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_1)'
2025-07-15 17:06:00,732 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_1)'
2025-07-15 17:06:00,732 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: inspections.as_is_condition -> '(Checkbox_1)'
2025-07-15 17:06:00,732 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_2)
2025-07-15 17:06:00,732 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 17:06:00,733 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_2) appearance keys: ['/Checkbox_2', '/Off']
2025-07-15 17:06:00,733 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,733 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_2) to UNCHECKED (/Off)
2025-07-15 17:06:00,733 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_2)
2025-07-15 17:06:00,733 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_2): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,734 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_2): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,734 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_2)'
2025-07-15 17:06:00,735 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_2)'
2025-07-15 17:06:00,735 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: inspections.seller_repairs -> '(Checkbox_2)'
2025-07-15 17:06:00,736 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_3)
2025-07-15 17:06:00,736 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 17:06:00,736 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_3) appearance keys: ['/Checkbox_3', '/Off']
2025-07-15 17:06:00,736 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,736 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_3) to CHECKED (/Yes)
2025-07-15 17:06:00,736 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_3)
2025-07-15 17:06:00,737 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,737 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_3): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,737 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_3)'
2025-07-15 17:06:00,739 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_3)'
2025-07-15 17:06:00,739 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: inspections.buyer_inspection -> '(Checkbox_3)'
2025-07-15 17:06:00,740 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_4)
2025-07-15 17:06:00,740 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 17:06:00,740 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_4) appearance keys: ['/Checkbox_4', '/Off']
2025-07-15 17:06:00,740 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,740 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_4) to CHECKED (/Yes)
2025-07-15 17:06:00,741 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_4)
2025-07-15 17:06:00,741 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,741 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_4): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,741 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_4)'
2025-07-15 17:06:00,742 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_4)'
2025-07-15 17:06:00,743 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: inspections.professional_inspection -> '(Checkbox_4)'
2025-07-15 17:06:00,743 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_5)
2025-07-15 17:06:00,743 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 17:06:00,744 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_5) appearance keys: ['/Checkbox_5', '/Off']
2025-07-15 17:06:00,744 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,744 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_5) to UNCHECKED (/Off)
2025-07-15 17:06:00,744 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_5)
2025-07-15 17:06:00,744 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_5): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,744 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_5): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,744 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_5)'
2025-07-15 17:06:00,746 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_5)'
2025-07-15 17:06:00,746 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: inspections.termite_inspection -> '(Checkbox_5)'
2025-07-15 17:06:00,746 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_6)
2025-07-15 17:06:00,746 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 17:06:00,746 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_6) appearance keys: ['/Checkbox_6', '/Off']
2025-07-15 17:06:00,746 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,746 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_6) to CHECKED (/Yes)
2025-07-15 17:06:00,747 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_6)
2025-07-15 17:06:00,747 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_6): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,747 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_6): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,747 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_6)'
2025-07-15 17:06:00,748 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_6)'
2025-07-15 17:06:00,748 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: inspections.roof_inspection -> '(Checkbox_6)'
2025-07-15 17:06:00,750 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_28)
2025-07-15 17:06:00,750 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 17:06:00,750 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_28) appearance keys: ['/Checkbox_28', '/Off']
2025-07-15 17:06:00,750 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,750 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_28) to CHECKED (/Yes)
2025-07-15 17:06:00,750 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_28)
2025-07-15 17:06:00,750 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_28): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,750 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_28): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,750 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_28)'
2025-07-15 17:06:00,752 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_28)'
2025-07-15 17:06:00,752 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: appliances.refrigerator -> '(Checkbox_28)'
2025-07-15 17:06:00,752 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_29)
2025-07-15 17:06:00,752 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 17:06:00,752 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_29) appearance keys: ['/Checkbox_29', '/Off']
2025-07-15 17:06:00,753 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,753 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_29) to UNCHECKED (/Off)
2025-07-15 17:06:00,753 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_29)
2025-07-15 17:06:00,753 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_29): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,753 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_29): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,753 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_29)'
2025-07-15 17:06:00,754 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_29)'
2025-07-15 17:06:00,754 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: appliances.washer -> '(Checkbox_29)'
2025-07-15 17:06:00,755 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_30)
2025-07-15 17:06:00,755 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 17:06:00,756 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_30) appearance keys: ['/Checkbox_30', '/Off']
2025-07-15 17:06:00,756 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,756 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_30) to CHECKED (/Yes)
2025-07-15 17:06:00,756 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_30)
2025-07-15 17:06:00,756 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_30): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,756 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_30): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,756 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_30)'
2025-07-15 17:06:00,757 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_30)'
2025-07-15 17:06:00,757 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: appliances.dryer -> '(Checkbox_30)'
2025-07-15 17:06:00,758 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_31)
2025-07-15 17:06:00,758 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 17:06:00,759 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_31) appearance keys: ['/Checkbox_31', '/Off']
2025-07-15 17:06:00,759 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,759 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_31) to CHECKED (/Yes)
2025-07-15 17:06:00,759 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_31)
2025-07-15 17:06:00,759 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_31): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,759 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_31): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,759 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_31)'
2025-07-15 17:06:00,760 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_31)'
2025-07-15 17:06:00,760 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: appliances.dishwasher -> '(Checkbox_31)'
2025-07-15 17:06:00,762 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_32)
2025-07-15 17:06:00,762 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 17:06:00,762 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_32) appearance keys: ['/Checkbox_32', '/Off']
2025-07-15 17:06:00,762 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,762 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_32) to UNCHECKED (/Off)
2025-07-15 17:06:00,762 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_32)
2025-07-15 17:06:00,762 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_32): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,762 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_32): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,762 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_32)'
2025-07-15 17:06:00,763 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_32)'
2025-07-15 17:06:00,764 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: appliances.microwave -> '(Checkbox_32)'
2025-07-15 17:06:00,764 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_33)
2025-07-15 17:06:00,764 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 17:06:00,765 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_33) appearance keys: ['/Checkbox_33', '/Off']
2025-07-15 17:06:00,765 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 17:06:00,765 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_33) to CHECKED (/Yes)
2025-07-15 17:06:00,765 - __main__ - DEBUG - _ensure_appearance_dict:255 - Adding /Yes appearance state for (Checkbox_33)
2025-07-15 17:06:00,765 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_33): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,765 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_33): Dict key '/Yes' is not a PdfName
2025-07-15 17:06:00,765 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_33)'
2025-07-15 17:06:00,766 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_33)'
2025-07-15 17:06:00,766 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: appliances.oven_range -> '(Checkbox_33)'
2025-07-15 17:06:00,828 - __main__ - INFO - fill_pdf:1044 - PDF generated successfully. Processed: 3, Failed: 18
2025-07-15 17:06:00,828 - __main__ - INFO - fill_pdf:1207 - PDF generated successfully. Fields processed: 3, Failed: 18
2025-07-15 17:06:00,829 - __main__ - INFO - fill_pdf:1243 - Checkbox debug mode disabled
2025-07-15 17:06:00,829 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 17:06:00] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 20:26:05,988 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:26:05,993 - __main__ - INFO - _load_field_mapping:656 - Loaded field mapping with 115 fields
2025-07-15 20:26:05,994 - __main__ - WARNING - _load_field_mapping_guide:674 - Field mapping guide not found
2025-07-15 20:26:05,995 - __main__ - INFO - _load_pdf_fields_info:686 - Loaded 188 PDF field definitions
2025-07-15 20:27:02,850 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:27:02,863 - __main__ - INFO - _load_field_mapping:656 - Loaded field mapping with 115 fields
2025-07-15 20:27:02,863 - __main__ - WARNING - _load_field_mapping_guide:674 - Field mapping guide not found
2025-07-15 20:27:02,864 - __main__ - INFO - _load_pdf_fields_info:686 - Loaded 188 PDF field definitions
2025-07-15 20:27:03,215 - werkzeug - INFO - _log:187 - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-15 20:27:03,215 - werkzeug - INFO - _log:187 - [33mPress CTRL+C to quit[0m
2025-07-15 20:27:03,242 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 20:27:03,493 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:27:03,493 - __main__ - INFO - _load_field_mapping:656 - Loaded field mapping with 115 fields
2025-07-15 20:27:03,493 - __main__ - WARNING - _load_field_mapping_guide:674 - Field mapping guide not found
2025-07-15 20:27:03,494 - __main__ - INFO - _load_pdf_fields_info:686 - Loaded 188 PDF field definitions
2025-07-15 20:27:03,507 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 20:27:03,520 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 20:27:34,462 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:27:34] "GET /debug-checkboxes HTTP/1.1" 200 -
2025-07-15 20:28:05,723 - __main__ - INFO - fill_pdf:1189 - Received PDF fill request with 2 fields
2025-07-15 20:28:05,823 - __main__ - INFO - fill_pdf:1007 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 20:28:05,826 - __main__ - WARNING - _ensure_appearance_dict:269 - Could not ensure appearance dictionary for (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 20:28:05,827 - __main__ - ERROR - _handle_button_field:178 - Error setting checkbox (Checkbox_8): Dict key '/Yes' is not a PdfName
2025-07-15 20:28:05,827 - __main__ - ERROR - set_pdf_field:575 - Handler failed for field '(Checkbox_8)'
2025-07-15 20:28:05,829 - __main__ - WARNING - set_pdf_field:583 - Field not found in PDF: '(Checkbox_8)'
2025-07-15 20:28:05,829 - __main__ - WARNING - _fill_checkbox_group:1106 - Failed to fill checkbox: financing.cash -> '(Checkbox_8)'
2025-07-15 20:28:05,901 - __main__ - INFO - fill_pdf:1044 - PDF generated successfully. Processed: 1, Failed: 1
2025-07-15 20:28:05,902 - __main__ - INFO - fill_pdf:1207 - PDF generated successfully. Fields processed: 1, Failed: 1
2025-07-15 20:28:05,906 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:28:05] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 20:28:35,452 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-15 20:28:35,724 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 20:28:37,283 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:28:37,284 - __main__ - INFO - _load_field_mapping:656 - Loaded field mapping with 115 fields
2025-07-15 20:28:37,284 - __main__ - WARNING - _load_field_mapping_guide:674 - Field mapping guide not found
2025-07-15 20:28:37,285 - __main__ - INFO - _load_pdf_fields_info:686 - Loaded 188 PDF field definitions
2025-07-15 20:28:37,338 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 20:28:37,358 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 20:29:02,029 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-15 20:29:02,093 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 20:29:02,408 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:29:02,408 - __main__ - INFO - _load_field_mapping:656 - Loaded field mapping with 115 fields
2025-07-15 20:29:02,408 - __main__ - WARNING - _load_field_mapping_guide:674 - Field mapping guide not found
2025-07-15 20:29:02,409 - __main__ - INFO - _load_pdf_fields_info:686 - Loaded 188 PDF field definitions
2025-07-15 20:29:02,422 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 20:29:02,431 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 20:29:23,617 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-15 20:29:23,685 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 20:29:23,936 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:29:23,936 - __main__ - INFO - _load_field_mapping:659 - Loaded field mapping with 115 fields
2025-07-15 20:29:23,936 - __main__ - WARNING - _load_field_mapping_guide:677 - Field mapping guide not found
2025-07-15 20:29:23,937 - __main__ - INFO - _load_pdf_fields_info:689 - Loaded 188 PDF field definitions
2025-07-15 20:29:23,960 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 20:29:24,078 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 20:29:56,826 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:29:56] "GET /debug-checkboxes HTTP/1.1" 200 -
2025-07-15 20:29:56,835 - __main__ - INFO - fill_pdf:1192 - Received PDF fill request with 6 fields
2025-07-15 20:29:56,835 - __main__ - INFO - fill_pdf:1197 - Checkbox debug mode enabled
2025-07-15 20:29:56,883 - __main__ - INFO - fill_pdf:1010 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 20:29:56,884 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Text_1)' = Enhanced Test Seller
2025-07-15 20:29:56,885 - __main__ - DEBUG - _fill_regular_field:1076 - Successfully filled field: seller_name -> '(Text_1)' = Enhanced Test Seller
2025-07-15 20:29:56,887 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Text_2)' = Enhanced Test Buyer
2025-07-15 20:29:56,889 - __main__ - DEBUG - _fill_regular_field:1076 - Successfully filled field: buyer_name -> '(Text_2)' = Enhanced Test Buyer
2025-07-15 20:29:56,889 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Number_1)' = 850000
2025-07-15 20:29:56,891 - __main__ - DEBUG - _fill_regular_field:1076 - Successfully filled field: purchase_price -> '(Number_1)' = 850000
2025-07-15 20:29:56,891 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_8)
2025-07-15 20:29:56,891 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:29:56,892 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_8) appearance keys: ['/Checkbox_8', '/Off']
2025-07-15 20:29:56,892 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,892 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_8) to CHECKED (/Yes)
2025-07-15 20:29:56,892 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_8)
2025-07-15 20:29:56,892 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_8)
2025-07-15 20:29:56,892 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_8) to checked
2025-07-15 20:29:56,893 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_8)' = True
2025-07-15 20:29:56,894 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: financing.cash -> '(Checkbox_8)' = True
2025-07-15 20:29:56,895 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_9)
2025-07-15 20:29:56,895 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:29:56,896 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_9) appearance keys: ['/Checkbox_9', '/Off']
2025-07-15 20:29:56,896 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,896 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_9) to UNCHECKED (/Off)
2025-07-15 20:29:56,896 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_9)
2025-07-15 20:29:56,897 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_9)
2025-07-15 20:29:56,897 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_9) to unchecked
2025-07-15 20:29:56,897 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_9)' = False
2025-07-15 20:29:56,899 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: financing.conventional -> '(Checkbox_9)' = False
2025-07-15 20:29:56,899 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_10)
2025-07-15 20:29:56,899 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:29:56,900 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_10) appearance keys: ['/Checkbox_10', '/Off']
2025-07-15 20:29:56,900 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,900 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_10) to CHECKED (/Yes)
2025-07-15 20:29:56,901 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_10)
2025-07-15 20:29:56,901 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_10)
2025-07-15 20:29:56,901 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_10) to checked
2025-07-15 20:29:56,902 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_10)' = True
2025-07-15 20:29:56,903 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: financing.fha -> '(Checkbox_10)' = True
2025-07-15 20:29:56,904 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_11)
2025-07-15 20:29:56,904 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:29:56,905 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_11) appearance keys: ['/Checkbox_11', '/Off']
2025-07-15 20:29:56,905 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,905 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_11) to UNCHECKED (/Off)
2025-07-15 20:29:56,905 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_11)
2025-07-15 20:29:56,906 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_11)
2025-07-15 20:29:56,906 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_11) to unchecked
2025-07-15 20:29:56,906 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_11)' = False
2025-07-15 20:29:56,908 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: financing.va -> '(Checkbox_11)' = False
2025-07-15 20:29:56,908 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_12)
2025-07-15 20:29:56,908 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:29:56,908 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_12) appearance keys: ['/Checkbox_12', '/Off']
2025-07-15 20:29:56,908 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,909 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_12) to UNCHECKED (/Off)
2025-07-15 20:29:56,909 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_12)
2025-07-15 20:29:56,909 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_12)
2025-07-15 20:29:56,909 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_12) to unchecked
2025-07-15 20:29:56,909 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_12)' = False
2025-07-15 20:29:56,910 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: financing.usda -> '(Checkbox_12)' = False
2025-07-15 20:29:56,911 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_13)
2025-07-15 20:29:56,911 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:29:56,911 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_13) appearance keys: ['/Checkbox_13', '/Off']
2025-07-15 20:29:56,912 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,912 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_13) to UNCHECKED (/Off)
2025-07-15 20:29:56,912 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_13)
2025-07-15 20:29:56,912 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_13)
2025-07-15 20:29:56,912 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_13) to unchecked
2025-07-15 20:29:56,912 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_13)' = False
2025-07-15 20:29:56,914 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: financing.other -> '(Checkbox_13)' = False
2025-07-15 20:29:56,914 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_1)
2025-07-15 20:29:56,914 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:29:56,914 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_1) appearance keys: ['/Checkbox_1', '/Off']
2025-07-15 20:29:56,914 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,914 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_1) to CHECKED (/Yes)
2025-07-15 20:29:56,914 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_1)
2025-07-15 20:29:56,914 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_1)
2025-07-15 20:29:56,915 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_1) to checked
2025-07-15 20:29:56,915 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_1)' = True
2025-07-15 20:29:56,916 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: inspections.as_is_condition -> '(Checkbox_1)' = True
2025-07-15 20:29:56,916 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_2)
2025-07-15 20:29:56,916 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:29:56,917 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_2) appearance keys: ['/Checkbox_2', '/Off']
2025-07-15 20:29:56,917 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,917 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_2) to UNCHECKED (/Off)
2025-07-15 20:29:56,918 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_2)
2025-07-15 20:29:56,918 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_2)
2025-07-15 20:29:56,918 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_2) to unchecked
2025-07-15 20:29:56,918 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_2)' = False
2025-07-15 20:29:56,920 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: inspections.seller_repairs -> '(Checkbox_2)' = False
2025-07-15 20:29:56,920 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_3)
2025-07-15 20:29:56,920 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:29:56,921 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_3) appearance keys: ['/Checkbox_3', '/Off']
2025-07-15 20:29:56,921 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,921 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_3) to CHECKED (/Yes)
2025-07-15 20:29:56,921 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_3)
2025-07-15 20:29:56,921 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_3)
2025-07-15 20:29:56,922 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_3) to checked
2025-07-15 20:29:56,922 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_3)' = True
2025-07-15 20:29:56,923 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: inspections.buyer_inspection -> '(Checkbox_3)' = True
2025-07-15 20:29:56,924 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_4)
2025-07-15 20:29:56,924 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:29:56,924 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_4) appearance keys: ['/Checkbox_4', '/Off']
2025-07-15 20:29:56,924 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,924 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_4) to CHECKED (/Yes)
2025-07-15 20:29:56,924 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_4)
2025-07-15 20:29:56,924 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_4)
2025-07-15 20:29:56,924 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_4) to checked
2025-07-15 20:29:56,924 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_4)' = True
2025-07-15 20:29:56,926 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: inspections.professional_inspection -> '(Checkbox_4)' = True
2025-07-15 20:29:56,926 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_5)
2025-07-15 20:29:56,926 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:29:56,927 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_5) appearance keys: ['/Checkbox_5', '/Off']
2025-07-15 20:29:56,927 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,927 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_5) to UNCHECKED (/Off)
2025-07-15 20:29:56,927 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_5)
2025-07-15 20:29:56,927 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_5)
2025-07-15 20:29:56,927 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_5) to unchecked
2025-07-15 20:29:56,927 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_5)' = False
2025-07-15 20:29:56,929 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: inspections.termite_inspection -> '(Checkbox_5)' = False
2025-07-15 20:29:56,929 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_6)
2025-07-15 20:29:56,929 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:29:56,929 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_6) appearance keys: ['/Checkbox_6', '/Off']
2025-07-15 20:29:56,929 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,930 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_6) to CHECKED (/Yes)
2025-07-15 20:29:56,930 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_6)
2025-07-15 20:29:56,930 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_6)
2025-07-15 20:29:56,930 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_6) to checked
2025-07-15 20:29:56,930 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_6)' = True
2025-07-15 20:29:56,931 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: inspections.roof_inspection -> '(Checkbox_6)' = True
2025-07-15 20:29:56,932 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_28)
2025-07-15 20:29:56,933 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:29:56,933 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_28) appearance keys: ['/Checkbox_28', '/Off']
2025-07-15 20:29:56,933 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,933 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_28) to CHECKED (/Yes)
2025-07-15 20:29:56,933 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_28)
2025-07-15 20:29:56,933 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_28)
2025-07-15 20:29:56,933 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_28) to checked
2025-07-15 20:29:56,933 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_28)' = True
2025-07-15 20:29:56,935 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: appliances.refrigerator -> '(Checkbox_28)' = True
2025-07-15 20:29:56,935 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_29)
2025-07-15 20:29:56,935 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:29:56,935 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_29) appearance keys: ['/Checkbox_29', '/Off']
2025-07-15 20:29:56,936 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,936 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_29) to UNCHECKED (/Off)
2025-07-15 20:29:56,936 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_29)
2025-07-15 20:29:56,936 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_29)
2025-07-15 20:29:56,936 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_29) to unchecked
2025-07-15 20:29:56,936 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_29)' = False
2025-07-15 20:29:56,937 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: appliances.washer -> '(Checkbox_29)' = False
2025-07-15 20:29:56,938 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_30)
2025-07-15 20:29:56,938 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:29:56,938 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_30) appearance keys: ['/Checkbox_30', '/Off']
2025-07-15 20:29:56,939 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,939 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_30) to CHECKED (/Yes)
2025-07-15 20:29:56,939 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_30)
2025-07-15 20:29:56,939 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_30)
2025-07-15 20:29:56,939 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_30) to checked
2025-07-15 20:29:56,939 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_30)' = True
2025-07-15 20:29:56,940 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: appliances.dryer -> '(Checkbox_30)' = True
2025-07-15 20:29:56,941 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_31)
2025-07-15 20:29:56,941 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:29:56,941 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_31) appearance keys: ['/Checkbox_31', '/Off']
2025-07-15 20:29:56,941 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,941 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_31) to CHECKED (/Yes)
2025-07-15 20:29:56,942 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_31)
2025-07-15 20:29:56,942 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_31)
2025-07-15 20:29:56,942 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_31) to checked
2025-07-15 20:29:56,942 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_31)' = True
2025-07-15 20:29:56,943 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: appliances.dishwasher -> '(Checkbox_31)' = True
2025-07-15 20:29:56,944 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_32)
2025-07-15 20:29:56,944 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:29:56,944 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_32) appearance keys: ['/Checkbox_32', '/Off']
2025-07-15 20:29:56,945 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,945 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_32) to UNCHECKED (/Off)
2025-07-15 20:29:56,945 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_32)
2025-07-15 20:29:56,945 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_32)
2025-07-15 20:29:56,945 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_32) to unchecked
2025-07-15 20:29:56,945 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_32)' = False
2025-07-15 20:29:56,946 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: appliances.microwave -> '(Checkbox_32)' = False
2025-07-15 20:29:56,947 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_33)
2025-07-15 20:29:56,947 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:29:56,947 - __main__ - DEBUG - _determine_checkbox_states:205 - Field (Checkbox_33) appearance keys: ['/Checkbox_33', '/Off']
2025-07-15 20:29:56,947 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:29:56,947 - __main__ - DEBUG - _handle_button_field:156 - Set checkbox (Checkbox_33) to CHECKED (/Yes)
2025-07-15 20:29:56,947 - __main__ - DEBUG - _ensure_appearance_dict:258 - Adding /Yes appearance state for (Checkbox_33)
2025-07-15 20:29:56,947 - __main__ - DEBUG - _ensure_appearance_dict:262 - Adding /Off appearance state for (Checkbox_33)
2025-07-15 20:29:56,948 - __main__ - INFO - _handle_button_field:173 - Successfully set checkbox (Checkbox_33) to checked
2025-07-15 20:29:56,948 - __main__ - DEBUG - set_pdf_field:576 - Successfully set field '(Checkbox_33)' = True
2025-07-15 20:29:56,949 - __main__ - DEBUG - _fill_checkbox_group:1107 - Successfully filled checkbox: appliances.oven_range -> '(Checkbox_33)' = True
2025-07-15 20:29:57,007 - __main__ - INFO - fill_pdf:1047 - PDF generated successfully. Processed: 21, Failed: 0
2025-07-15 20:29:57,007 - __main__ - INFO - fill_pdf:1210 - PDF generated successfully. Fields processed: 21, Failed: 0
2025-07-15 20:29:57,007 - __main__ - INFO - fill_pdf:1246 - Checkbox debug mode disabled
2025-07-15 20:29:57,008 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:29:57] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 20:30:15,186 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-15 20:30:15,240 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 20:30:15,491 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:30:15,491 - __main__ - INFO - _load_field_mapping:663 - Loaded field mapping with 115 fields
2025-07-15 20:30:15,492 - __main__ - WARNING - _load_field_mapping_guide:681 - Field mapping guide not found
2025-07-15 20:30:15,492 - __main__ - INFO - _load_pdf_fields_info:693 - Loaded 188 PDF field definitions
2025-07-15 20:30:15,505 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 20:30:15,514 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 20:30:43,674 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-15 20:30:43,726 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 20:30:44,031 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:30:44,031 - __main__ - INFO - _load_field_mapping:663 - Loaded field mapping with 115 fields
2025-07-15 20:30:44,032 - __main__ - WARNING - _load_field_mapping_guide:681 - Field mapping guide not found
2025-07-15 20:30:44,032 - __main__ - INFO - _load_pdf_fields_info:693 - Loaded 188 PDF field definitions
2025-07-15 20:30:44,046 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 20:30:44,061 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 20:31:10,315 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-15 20:31:10,366 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 20:31:10,631 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:31:10,632 - __main__ - INFO - _load_field_mapping:666 - Loaded field mapping with 115 fields
2025-07-15 20:31:10,632 - __main__ - WARNING - _load_field_mapping_guide:684 - Field mapping guide not found
2025-07-15 20:31:10,633 - __main__ - INFO - _load_pdf_fields_info:696 - Loaded 188 PDF field definitions
2025-07-15 20:31:10,648 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 20:31:10,662 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 20:31:31,782 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/comprehensive_checkbox_fix.py', reloading
2025-07-15 20:31:31,867 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 20:31:32,160 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:31:32,161 - __main__ - INFO - _load_field_mapping:666 - Loaded field mapping with 115 fields
2025-07-15 20:31:32,161 - __main__ - WARNING - _load_field_mapping_guide:684 - Field mapping guide not found
2025-07-15 20:31:32,161 - __main__ - INFO - _load_pdf_fields_info:696 - Loaded 188 PDF field definitions
2025-07-15 20:31:32,174 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 20:31:32,184 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 20:32:20,285 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:32:20,286 - __main__ - INFO - _load_field_mapping:666 - Loaded field mapping with 115 fields
2025-07-15 20:32:20,286 - __main__ - WARNING - _load_field_mapping_guide:684 - Field mapping guide not found
2025-07-15 20:32:20,286 - __main__ - INFO - _load_pdf_fields_info:696 - Loaded 188 PDF field definitions
2025-07-15 20:32:20,306 - werkzeug - INFO - _log:187 - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-15 20:32:20,306 - werkzeug - INFO - _log:187 - [33mPress CTRL+C to quit[0m
2025-07-15 20:32:20,310 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 20:32:20,562 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:32:20,562 - __main__ - INFO - _load_field_mapping:666 - Loaded field mapping with 115 fields
2025-07-15 20:32:20,562 - __main__ - WARNING - _load_field_mapping_guide:684 - Field mapping guide not found
2025-07-15 20:32:20,563 - __main__ - INFO - _load_pdf_fields_info:696 - Loaded 188 PDF field definitions
2025-07-15 20:32:20,577 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 20:32:20,590 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 20:33:01,747 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:33:01] "GET /debug-checkboxes HTTP/1.1" 200 -
2025-07-15 20:33:01,753 - __main__ - INFO - fill_pdf:1199 - Received PDF fill request with 6 fields
2025-07-15 20:33:01,754 - __main__ - INFO - fill_pdf:1204 - Checkbox debug mode enabled
2025-07-15 20:33:01,801 - __main__ - INFO - fill_pdf:1017 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 20:33:01,801 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Text_1)' = Enhanced Test Seller
2025-07-15 20:33:01,803 - __main__ - DEBUG - _fill_regular_field:1083 - Successfully filled field: seller_name -> '(Text_1)' = Enhanced Test Seller
2025-07-15 20:33:01,803 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Text_2)' = Enhanced Test Buyer
2025-07-15 20:33:01,805 - __main__ - DEBUG - _fill_regular_field:1083 - Successfully filled field: buyer_name -> '(Text_2)' = Enhanced Test Buyer
2025-07-15 20:33:01,805 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Number_1)' = 850000
2025-07-15 20:33:01,807 - __main__ - DEBUG - _fill_regular_field:1083 - Successfully filled field: purchase_price -> '(Number_1)' = 850000
2025-07-15 20:33:01,807 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_8)
2025-07-15 20:33:01,807 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:33:01,808 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_8) appearance keys: ['/Checkbox_8', '/Off']
2025-07-15 20:33:01,808 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,808 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_8) to CHECKED (/Yes)
2025-07-15 20:33:01,808 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_8)
2025-07-15 20:33:01,808 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_8) to checked
2025-07-15 20:33:01,808 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_8)' = True
2025-07-15 20:33:01,810 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: financing.cash -> '(Checkbox_8)' = True
2025-07-15 20:33:01,810 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_9)
2025-07-15 20:33:01,810 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:33:01,810 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_9) appearance keys: ['/Checkbox_9', '/Off']
2025-07-15 20:33:01,810 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,810 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_9) to UNCHECKED (/Off)
2025-07-15 20:33:01,810 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_9)
2025-07-15 20:33:01,811 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_9) to unchecked
2025-07-15 20:33:01,811 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_9)' = False
2025-07-15 20:33:01,812 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: financing.conventional -> '(Checkbox_9)' = False
2025-07-15 20:33:01,812 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_10)
2025-07-15 20:33:01,813 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:33:01,813 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_10) appearance keys: ['/Checkbox_10', '/Off']
2025-07-15 20:33:01,813 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,814 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_10) to CHECKED (/Yes)
2025-07-15 20:33:01,814 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_10)
2025-07-15 20:33:01,814 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_10) to checked
2025-07-15 20:33:01,814 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_10)' = True
2025-07-15 20:33:01,815 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: financing.fha -> '(Checkbox_10)' = True
2025-07-15 20:33:01,816 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_11)
2025-07-15 20:33:01,816 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:33:01,816 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_11) appearance keys: ['/Checkbox_11', '/Off']
2025-07-15 20:33:01,816 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,816 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_11) to UNCHECKED (/Off)
2025-07-15 20:33:01,816 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_11)
2025-07-15 20:33:01,816 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_11) to unchecked
2025-07-15 20:33:01,816 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_11)' = False
2025-07-15 20:33:01,818 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: financing.va -> '(Checkbox_11)' = False
2025-07-15 20:33:01,818 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_12)
2025-07-15 20:33:01,818 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:33:01,819 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_12) appearance keys: ['/Checkbox_12', '/Off']
2025-07-15 20:33:01,819 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,819 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_12) to UNCHECKED (/Off)
2025-07-15 20:33:01,819 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_12)
2025-07-15 20:33:01,819 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_12) to unchecked
2025-07-15 20:33:01,819 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_12)' = False
2025-07-15 20:33:01,821 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: financing.usda -> '(Checkbox_12)' = False
2025-07-15 20:33:01,821 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_13)
2025-07-15 20:33:01,821 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:33:01,821 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_13) appearance keys: ['/Checkbox_13', '/Off']
2025-07-15 20:33:01,821 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,821 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_13) to UNCHECKED (/Off)
2025-07-15 20:33:01,822 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_13)
2025-07-15 20:33:01,822 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_13) to unchecked
2025-07-15 20:33:01,822 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_13)' = False
2025-07-15 20:33:01,823 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: financing.other -> '(Checkbox_13)' = False
2025-07-15 20:33:01,823 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_1)
2025-07-15 20:33:01,823 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:33:01,824 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_1) appearance keys: ['/Checkbox_1', '/Off']
2025-07-15 20:33:01,824 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,824 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_1) to CHECKED (/Yes)
2025-07-15 20:33:01,825 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_1)
2025-07-15 20:33:01,825 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_1) to checked
2025-07-15 20:33:01,825 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_1)' = True
2025-07-15 20:33:01,826 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: inspections.as_is_condition -> '(Checkbox_1)' = True
2025-07-15 20:33:01,827 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_2)
2025-07-15 20:33:01,827 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:33:01,829 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_2) appearance keys: ['/Checkbox_2', '/Off']
2025-07-15 20:33:01,829 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,830 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_2) to UNCHECKED (/Off)
2025-07-15 20:33:01,830 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_2)
2025-07-15 20:33:01,830 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_2) to unchecked
2025-07-15 20:33:01,830 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_2)' = False
2025-07-15 20:33:01,832 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: inspections.seller_repairs -> '(Checkbox_2)' = False
2025-07-15 20:33:01,832 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_3)
2025-07-15 20:33:01,832 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:33:01,832 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_3) appearance keys: ['/Checkbox_3', '/Off']
2025-07-15 20:33:01,832 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,832 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_3) to CHECKED (/Yes)
2025-07-15 20:33:01,833 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_3)
2025-07-15 20:33:01,833 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_3) to checked
2025-07-15 20:33:01,833 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_3)' = True
2025-07-15 20:33:01,834 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: inspections.buyer_inspection -> '(Checkbox_3)' = True
2025-07-15 20:33:01,834 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_4)
2025-07-15 20:33:01,834 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:33:01,835 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_4) appearance keys: ['/Checkbox_4', '/Off']
2025-07-15 20:33:01,836 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,836 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_4) to CHECKED (/Yes)
2025-07-15 20:33:01,836 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_4)
2025-07-15 20:33:01,836 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_4) to checked
2025-07-15 20:33:01,836 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_4)' = True
2025-07-15 20:33:01,837 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: inspections.professional_inspection -> '(Checkbox_4)' = True
2025-07-15 20:33:01,838 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_5)
2025-07-15 20:33:01,838 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:33:01,838 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_5) appearance keys: ['/Checkbox_5', '/Off']
2025-07-15 20:33:01,838 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,838 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_5) to UNCHECKED (/Off)
2025-07-15 20:33:01,838 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_5)
2025-07-15 20:33:01,838 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_5) to unchecked
2025-07-15 20:33:01,838 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_5)' = False
2025-07-15 20:33:01,841 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: inspections.termite_inspection -> '(Checkbox_5)' = False
2025-07-15 20:33:01,841 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_6)
2025-07-15 20:33:01,841 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:33:01,842 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_6) appearance keys: ['/Checkbox_6', '/Off']
2025-07-15 20:33:01,842 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,842 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_6) to CHECKED (/Yes)
2025-07-15 20:33:01,842 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_6)
2025-07-15 20:33:01,842 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_6) to checked
2025-07-15 20:33:01,842 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_6)' = True
2025-07-15 20:33:01,843 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: inspections.roof_inspection -> '(Checkbox_6)' = True
2025-07-15 20:33:01,844 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_28)
2025-07-15 20:33:01,844 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:33:01,844 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_28) appearance keys: ['/Checkbox_28', '/Off']
2025-07-15 20:33:01,844 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,844 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_28) to CHECKED (/Yes)
2025-07-15 20:33:01,845 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_28)
2025-07-15 20:33:01,845 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_28) to checked
2025-07-15 20:33:01,845 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_28)' = True
2025-07-15 20:33:01,846 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: appliances.refrigerator -> '(Checkbox_28)' = True
2025-07-15 20:33:01,847 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_29)
2025-07-15 20:33:01,847 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:33:01,847 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_29) appearance keys: ['/Checkbox_29', '/Off']
2025-07-15 20:33:01,847 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,847 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_29) to UNCHECKED (/Off)
2025-07-15 20:33:01,848 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_29)
2025-07-15 20:33:01,848 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_29) to unchecked
2025-07-15 20:33:01,848 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_29)' = False
2025-07-15 20:33:01,849 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: appliances.washer -> '(Checkbox_29)' = False
2025-07-15 20:33:01,849 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_30)
2025-07-15 20:33:01,849 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:33:01,850 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_30) appearance keys: ['/Checkbox_30', '/Off']
2025-07-15 20:33:01,850 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,850 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_30) to CHECKED (/Yes)
2025-07-15 20:33:01,850 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_30)
2025-07-15 20:33:01,850 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_30) to checked
2025-07-15 20:33:01,850 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_30)' = True
2025-07-15 20:33:01,851 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: appliances.dryer -> '(Checkbox_30)' = True
2025-07-15 20:33:01,852 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_31)
2025-07-15 20:33:01,852 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:33:01,852 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_31) appearance keys: ['/Checkbox_31', '/Off']
2025-07-15 20:33:01,853 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,853 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_31) to CHECKED (/Yes)
2025-07-15 20:33:01,853 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_31)
2025-07-15 20:33:01,853 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_31) to checked
2025-07-15 20:33:01,853 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_31)' = True
2025-07-15 20:33:01,854 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: appliances.dishwasher -> '(Checkbox_31)' = True
2025-07-15 20:33:01,855 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_32)
2025-07-15 20:33:01,855 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:33:01,855 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_32) appearance keys: ['/Checkbox_32', '/Off']
2025-07-15 20:33:01,855 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,855 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_32) to UNCHECKED (/Off)
2025-07-15 20:33:01,855 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_32)
2025-07-15 20:33:01,855 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_32) to unchecked
2025-07-15 20:33:01,855 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_32)' = False
2025-07-15 20:33:01,857 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: appliances.microwave -> '(Checkbox_32)' = False
2025-07-15 20:33:01,858 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_33)
2025-07-15 20:33:01,858 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:33:01,858 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_33) appearance keys: ['/Checkbox_33', '/Off']
2025-07-15 20:33:01,858 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:33:01,858 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_33) to CHECKED (/Yes)
2025-07-15 20:33:01,858 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_33)
2025-07-15 20:33:01,859 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_33) to checked
2025-07-15 20:33:01,859 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_33)' = True
2025-07-15 20:33:01,860 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: appliances.oven_range -> '(Checkbox_33)' = True
2025-07-15 20:33:01,917 - __main__ - INFO - fill_pdf:1054 - PDF generated successfully. Processed: 21, Failed: 0
2025-07-15 20:33:01,918 - __main__ - INFO - fill_pdf:1217 - PDF generated successfully. Fields processed: 21, Failed: 0
2025-07-15 20:33:01,918 - __main__ - INFO - fill_pdf:1253 - Checkbox debug mode disabled
2025-07-15 20:33:01,918 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:33:01] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 20:35:43,990 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:35:43] "GET /debug-checkboxes HTTP/1.1" 200 -
2025-07-15 20:35:43,999 - __main__ - INFO - fill_pdf:1199 - Received PDF fill request with 6 fields
2025-07-15 20:35:44,000 - __main__ - INFO - fill_pdf:1204 - Checkbox debug mode enabled
2025-07-15 20:35:44,060 - __main__ - INFO - fill_pdf:1017 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 20:35:44,060 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Text_1)' = Enhanced Test Seller
2025-07-15 20:35:44,062 - __main__ - DEBUG - _fill_regular_field:1083 - Successfully filled field: seller_name -> '(Text_1)' = Enhanced Test Seller
2025-07-15 20:35:44,063 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Text_2)' = Enhanced Test Buyer
2025-07-15 20:35:44,064 - __main__ - DEBUG - _fill_regular_field:1083 - Successfully filled field: buyer_name -> '(Text_2)' = Enhanced Test Buyer
2025-07-15 20:35:44,065 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Number_1)' = 850000
2025-07-15 20:35:44,066 - __main__ - DEBUG - _fill_regular_field:1083 - Successfully filled field: purchase_price -> '(Number_1)' = 850000
2025-07-15 20:35:44,067 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_8)
2025-07-15 20:35:44,067 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:35:44,067 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_8) appearance keys: ['/Checkbox_8', '/Off']
2025-07-15 20:35:44,067 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,067 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_8) to CHECKED (/Yes)
2025-07-15 20:35:44,067 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_8)
2025-07-15 20:35:44,068 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_8) to checked
2025-07-15 20:35:44,068 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_8)' = True
2025-07-15 20:35:44,069 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: financing.cash -> '(Checkbox_8)' = True
2025-07-15 20:35:44,069 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_9)
2025-07-15 20:35:44,069 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:35:44,070 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_9) appearance keys: ['/Checkbox_9', '/Off']
2025-07-15 20:35:44,070 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,070 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_9) to UNCHECKED (/Off)
2025-07-15 20:35:44,070 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_9)
2025-07-15 20:35:44,070 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_9) to unchecked
2025-07-15 20:35:44,070 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_9)' = False
2025-07-15 20:35:44,073 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: financing.conventional -> '(Checkbox_9)' = False
2025-07-15 20:35:44,074 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_10)
2025-07-15 20:35:44,075 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:35:44,076 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_10) appearance keys: ['/Checkbox_10', '/Off']
2025-07-15 20:35:44,076 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,076 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_10) to CHECKED (/Yes)
2025-07-15 20:35:44,076 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_10)
2025-07-15 20:35:44,076 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_10) to checked
2025-07-15 20:35:44,076 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_10)' = True
2025-07-15 20:35:44,078 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: financing.fha -> '(Checkbox_10)' = True
2025-07-15 20:35:44,078 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_11)
2025-07-15 20:35:44,079 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:35:44,080 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_11) appearance keys: ['/Checkbox_11', '/Off']
2025-07-15 20:35:44,080 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,080 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_11) to UNCHECKED (/Off)
2025-07-15 20:35:44,080 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_11)
2025-07-15 20:35:44,080 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_11) to unchecked
2025-07-15 20:35:44,080 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_11)' = False
2025-07-15 20:35:44,082 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: financing.va -> '(Checkbox_11)' = False
2025-07-15 20:35:44,082 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_12)
2025-07-15 20:35:44,082 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:35:44,082 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_12) appearance keys: ['/Checkbox_12', '/Off']
2025-07-15 20:35:44,082 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,082 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_12) to UNCHECKED (/Off)
2025-07-15 20:35:44,083 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_12)
2025-07-15 20:35:44,083 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_12) to unchecked
2025-07-15 20:35:44,083 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_12)' = False
2025-07-15 20:35:44,084 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: financing.usda -> '(Checkbox_12)' = False
2025-07-15 20:35:44,084 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_13)
2025-07-15 20:35:44,085 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:35:44,086 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_13) appearance keys: ['/Checkbox_13', '/Off']
2025-07-15 20:35:44,086 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,086 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_13) to UNCHECKED (/Off)
2025-07-15 20:35:44,086 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_13)
2025-07-15 20:35:44,086 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_13) to unchecked
2025-07-15 20:35:44,086 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_13)' = False
2025-07-15 20:35:44,088 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: financing.other -> '(Checkbox_13)' = False
2025-07-15 20:35:44,088 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_1)
2025-07-15 20:35:44,088 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:35:44,088 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_1) appearance keys: ['/Checkbox_1', '/Off']
2025-07-15 20:35:44,088 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,088 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_1) to CHECKED (/Yes)
2025-07-15 20:35:44,088 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_1)
2025-07-15 20:35:44,089 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_1) to checked
2025-07-15 20:35:44,089 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_1)' = True
2025-07-15 20:35:44,090 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: inspections.as_is_condition -> '(Checkbox_1)' = True
2025-07-15 20:35:44,090 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_2)
2025-07-15 20:35:44,092 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:35:44,093 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_2) appearance keys: ['/Checkbox_2', '/Off']
2025-07-15 20:35:44,093 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,093 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_2) to UNCHECKED (/Off)
2025-07-15 20:35:44,093 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_2)
2025-07-15 20:35:44,096 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_2) to unchecked
2025-07-15 20:35:44,096 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_2)' = False
2025-07-15 20:35:44,099 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: inspections.seller_repairs -> '(Checkbox_2)' = False
2025-07-15 20:35:44,099 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_3)
2025-07-15 20:35:44,099 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:35:44,099 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_3) appearance keys: ['/Checkbox_3', '/Off']
2025-07-15 20:35:44,099 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,099 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_3) to CHECKED (/Yes)
2025-07-15 20:35:44,100 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_3)
2025-07-15 20:35:44,100 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_3) to checked
2025-07-15 20:35:44,100 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_3)' = True
2025-07-15 20:35:44,101 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: inspections.buyer_inspection -> '(Checkbox_3)' = True
2025-07-15 20:35:44,102 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_4)
2025-07-15 20:35:44,102 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:35:44,102 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_4) appearance keys: ['/Checkbox_4', '/Off']
2025-07-15 20:35:44,103 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,103 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_4) to CHECKED (/Yes)
2025-07-15 20:35:44,103 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_4)
2025-07-15 20:35:44,104 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_4) to checked
2025-07-15 20:35:44,104 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_4)' = True
2025-07-15 20:35:44,105 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: inspections.professional_inspection -> '(Checkbox_4)' = True
2025-07-15 20:35:44,105 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_5)
2025-07-15 20:35:44,105 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:35:44,106 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_5) appearance keys: ['/Checkbox_5', '/Off']
2025-07-15 20:35:44,106 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,106 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_5) to UNCHECKED (/Off)
2025-07-15 20:35:44,106 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_5)
2025-07-15 20:35:44,106 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_5) to unchecked
2025-07-15 20:35:44,106 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_5)' = False
2025-07-15 20:35:44,109 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: inspections.termite_inspection -> '(Checkbox_5)' = False
2025-07-15 20:35:44,109 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_6)
2025-07-15 20:35:44,109 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:35:44,110 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_6) appearance keys: ['/Checkbox_6', '/Off']
2025-07-15 20:35:44,110 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,110 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_6) to CHECKED (/Yes)
2025-07-15 20:35:44,110 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_6)
2025-07-15 20:35:44,110 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_6) to checked
2025-07-15 20:35:44,110 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_6)' = True
2025-07-15 20:35:44,111 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: inspections.roof_inspection -> '(Checkbox_6)' = True
2025-07-15 20:35:44,112 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_28)
2025-07-15 20:35:44,112 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:35:44,112 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_28) appearance keys: ['/Checkbox_28', '/Off']
2025-07-15 20:35:44,112 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,112 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_28) to CHECKED (/Yes)
2025-07-15 20:35:44,113 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_28)
2025-07-15 20:35:44,113 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_28) to checked
2025-07-15 20:35:44,113 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_28)' = True
2025-07-15 20:35:44,114 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: appliances.refrigerator -> '(Checkbox_28)' = True
2025-07-15 20:35:44,116 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_29)
2025-07-15 20:35:44,117 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:35:44,117 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_29) appearance keys: ['/Checkbox_29', '/Off']
2025-07-15 20:35:44,118 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,118 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_29) to UNCHECKED (/Off)
2025-07-15 20:35:44,118 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_29)
2025-07-15 20:35:44,119 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_29) to unchecked
2025-07-15 20:35:44,119 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_29)' = False
2025-07-15 20:35:44,120 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: appliances.washer -> '(Checkbox_29)' = False
2025-07-15 20:35:44,121 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_30)
2025-07-15 20:35:44,121 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:35:44,122 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_30) appearance keys: ['/Checkbox_30', '/Off']
2025-07-15 20:35:44,123 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,123 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_30) to CHECKED (/Yes)
2025-07-15 20:35:44,123 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_30)
2025-07-15 20:35:44,123 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_30) to checked
2025-07-15 20:35:44,123 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_30)' = True
2025-07-15 20:35:44,125 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: appliances.dryer -> '(Checkbox_30)' = True
2025-07-15 20:35:44,125 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_31)
2025-07-15 20:35:44,126 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:35:44,126 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_31) appearance keys: ['/Checkbox_31', '/Off']
2025-07-15 20:35:44,126 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,126 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_31) to CHECKED (/Yes)
2025-07-15 20:35:44,126 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_31)
2025-07-15 20:35:44,127 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_31) to checked
2025-07-15 20:35:44,127 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_31)' = True
2025-07-15 20:35:44,128 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: appliances.dishwasher -> '(Checkbox_31)' = True
2025-07-15 20:35:44,129 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_32)
2025-07-15 20:35:44,129 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:35:44,129 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_32) appearance keys: ['/Checkbox_32', '/Off']
2025-07-15 20:35:44,129 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,129 - __main__ - DEBUG - _handle_button_field:164 - Set checkbox (Checkbox_32) to UNCHECKED (/Off)
2025-07-15 20:35:44,129 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_32)
2025-07-15 20:35:44,129 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_32) to unchecked
2025-07-15 20:35:44,130 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_32)' = False
2025-07-15 20:35:44,131 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: appliances.microwave -> '(Checkbox_32)' = False
2025-07-15 20:35:44,131 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_33)
2025-07-15 20:35:44,131 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:35:44,132 - __main__ - DEBUG - _determine_checkbox_states:209 - Field (Checkbox_33) appearance keys: ['/Checkbox_33', '/Off']
2025-07-15 20:35:44,132 - __main__ - DEBUG - _handle_button_field:150 - Using states - On: /Yes, Off: /Off
2025-07-15 20:35:44,132 - __main__ - DEBUG - _handle_button_field:160 - Set checkbox (Checkbox_33) to CHECKED (/Yes)
2025-07-15 20:35:44,133 - __main__ - DEBUG - _ensure_appearance_dict:265 - Adding /Yes appearance state for (Checkbox_33)
2025-07-15 20:35:44,133 - __main__ - INFO - _handle_button_field:177 - Successfully set checkbox (Checkbox_33) to checked
2025-07-15 20:35:44,133 - __main__ - DEBUG - set_pdf_field:583 - Successfully set field '(Checkbox_33)' = True
2025-07-15 20:35:44,134 - __main__ - DEBUG - _fill_checkbox_group:1114 - Successfully filled checkbox: appliances.oven_range -> '(Checkbox_33)' = True
2025-07-15 20:35:44,194 - __main__ - INFO - fill_pdf:1054 - PDF generated successfully. Processed: 21, Failed: 0
2025-07-15 20:35:44,194 - __main__ - INFO - fill_pdf:1217 - PDF generated successfully. Fields processed: 21, Failed: 0
2025-07-15 20:35:44,194 - __main__ - INFO - fill_pdf:1253 - Checkbox debug mode disabled
2025-07-15 20:35:44,195 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:35:44] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 20:38:31,170 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-15 20:38:31,743 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 20:38:32,681 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:38:32,682 - __main__ - INFO - _load_field_mapping:665 - Loaded field mapping with 115 fields
2025-07-15 20:38:32,682 - __main__ - WARNING - _load_field_mapping_guide:683 - Field mapping guide not found
2025-07-15 20:38:32,687 - __main__ - INFO - _load_pdf_fields_info:695 - Loaded 188 PDF field definitions
2025-07-15 20:38:32,742 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 20:38:32,765 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 20:39:16,315 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-15 20:39:16,574 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 20:39:17,455 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:39:17,457 - __main__ - INFO - _load_field_mapping:715 - Loaded field mapping with 115 fields
2025-07-15 20:39:17,457 - __main__ - WARNING - _load_field_mapping_guide:733 - Field mapping guide not found
2025-07-15 20:39:17,459 - __main__ - INFO - _load_pdf_fields_info:745 - Loaded 188 PDF field definitions
2025-07-15 20:39:17,542 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 20:39:17,574 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 20:39:48,175 - __main__ - INFO - fill_pdf:1248 - Received PDF fill request with 2 fields
2025-07-15 20:39:48,175 - __main__ - INFO - fill_pdf:1253 - Checkbox debug mode enabled
2025-07-15 20:39:48,249 - __main__ - INFO - fill_pdf:1066 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 20:39:48,249 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_1)' = Test Seller
2025-07-15 20:39:48,254 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: seller_name -> '(Text_1)' = Test Seller
2025-07-15 20:39:48,261 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_8)
2025-07-15 20:39:48,262 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:39:48,266 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_8) existing appearance keys: ['/Checkbox_8', '/Off']
2025-07-15 20:39:48,266 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_8) using field-specific on state: /Checkbox_8
2025-07-15 20:39:48,266 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_8) detected states - On: /Checkbox_8, Off: /Off
2025-07-15 20:39:48,267 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_8, Off: /Off
2025-07-15 20:39:48,268 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_8) to CHECKED (/Checkbox_8)
2025-07-15 20:39:48,274 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_8) to checked
2025-07-15 20:39:48,275 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_8)' = True
2025-07-15 20:39:48,278 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.cash -> '(Checkbox_8)' = True
2025-07-15 20:39:48,286 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_9)
2025-07-15 20:39:48,291 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:39:48,291 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_9) existing appearance keys: ['/Checkbox_9', '/Off']
2025-07-15 20:39:48,292 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_9) using field-specific on state: /Checkbox_9
2025-07-15 20:39:48,292 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_9) detected states - On: /Checkbox_9, Off: /Off
2025-07-15 20:39:48,292 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_9, Off: /Off
2025-07-15 20:39:48,292 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_9) to UNCHECKED (/Off)
2025-07-15 20:39:48,292 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_9) to unchecked
2025-07-15 20:39:48,293 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_9)' = False
2025-07-15 20:39:48,294 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.conventional -> '(Checkbox_9)' = False
2025-07-15 20:39:48,416 - __main__ - INFO - fill_pdf:1103 - PDF generated successfully. Processed: 3, Failed: 0
2025-07-15 20:39:48,418 - __main__ - INFO - fill_pdf:1266 - PDF generated successfully. Fields processed: 3, Failed: 0
2025-07-15 20:39:48,426 - __main__ - INFO - fill_pdf:1302 - Checkbox debug mode disabled
2025-07-15 20:39:48,426 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:39:48] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 20:41:38,258 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:41:38] "GET /debug-checkboxes HTTP/1.1" 200 -
2025-07-15 20:41:38,267 - __main__ - INFO - fill_pdf:1248 - Received PDF fill request with 6 fields
2025-07-15 20:41:38,267 - __main__ - INFO - fill_pdf:1253 - Checkbox debug mode enabled
2025-07-15 20:41:38,327 - __main__ - INFO - fill_pdf:1066 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 20:41:38,327 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_1)' = Enhanced Test Seller
2025-07-15 20:41:38,329 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: seller_name -> '(Text_1)' = Enhanced Test Seller
2025-07-15 20:41:38,329 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_2)' = Enhanced Test Buyer
2025-07-15 20:41:38,331 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: buyer_name -> '(Text_2)' = Enhanced Test Buyer
2025-07-15 20:41:38,331 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Number_1)' = 850000
2025-07-15 20:41:38,333 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: purchase_price -> '(Number_1)' = 850000
2025-07-15 20:41:38,334 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_8)
2025-07-15 20:41:38,334 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:41:38,334 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_8) existing appearance keys: ['/Checkbox_8', '/Off']
2025-07-15 20:41:38,335 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_8) using field-specific on state: /Checkbox_8
2025-07-15 20:41:38,335 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_8) detected states - On: /Checkbox_8, Off: /Off
2025-07-15 20:41:38,335 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_8, Off: /Off
2025-07-15 20:41:38,335 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_8) to CHECKED (/Checkbox_8)
2025-07-15 20:41:38,335 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_8) to checked
2025-07-15 20:41:38,335 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_8)' = True
2025-07-15 20:41:38,336 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.cash -> '(Checkbox_8)' = True
2025-07-15 20:41:38,337 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_9)
2025-07-15 20:41:38,337 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:41:38,337 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_9) existing appearance keys: ['/Checkbox_9', '/Off']
2025-07-15 20:41:38,337 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_9) using field-specific on state: /Checkbox_9
2025-07-15 20:41:38,337 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_9) detected states - On: /Checkbox_9, Off: /Off
2025-07-15 20:41:38,337 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_9, Off: /Off
2025-07-15 20:41:38,337 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_9) to UNCHECKED (/Off)
2025-07-15 20:41:38,338 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_9) to unchecked
2025-07-15 20:41:38,338 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_9)' = False
2025-07-15 20:41:38,339 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.conventional -> '(Checkbox_9)' = False
2025-07-15 20:41:38,339 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_10)
2025-07-15 20:41:38,340 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:41:38,340 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_10) existing appearance keys: ['/Checkbox_10', '/Off']
2025-07-15 20:41:38,340 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_10) using field-specific on state: /Checkbox_10
2025-07-15 20:41:38,340 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_10) detected states - On: /Checkbox_10, Off: /Off
2025-07-15 20:41:38,340 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_10, Off: /Off
2025-07-15 20:41:38,341 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_10) to CHECKED (/Checkbox_10)
2025-07-15 20:41:38,341 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_10) to checked
2025-07-15 20:41:38,341 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_10)' = True
2025-07-15 20:41:38,342 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.fha -> '(Checkbox_10)' = True
2025-07-15 20:41:38,342 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_11)
2025-07-15 20:41:38,343 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:41:38,343 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_11) existing appearance keys: ['/Checkbox_11', '/Off']
2025-07-15 20:41:38,343 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_11) using field-specific on state: /Checkbox_11
2025-07-15 20:41:38,343 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_11) detected states - On: /Checkbox_11, Off: /Off
2025-07-15 20:41:38,343 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_11, Off: /Off
2025-07-15 20:41:38,343 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_11) to UNCHECKED (/Off)
2025-07-15 20:41:38,343 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_11) to unchecked
2025-07-15 20:41:38,343 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_11)' = False
2025-07-15 20:41:38,345 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.va -> '(Checkbox_11)' = False
2025-07-15 20:41:38,345 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_12)
2025-07-15 20:41:38,345 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:41:38,346 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_12) existing appearance keys: ['/Checkbox_12', '/Off']
2025-07-15 20:41:38,346 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_12) using field-specific on state: /Checkbox_12
2025-07-15 20:41:38,346 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_12) detected states - On: /Checkbox_12, Off: /Off
2025-07-15 20:41:38,346 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_12, Off: /Off
2025-07-15 20:41:38,346 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_12) to UNCHECKED (/Off)
2025-07-15 20:41:38,346 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_12) to unchecked
2025-07-15 20:41:38,346 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_12)' = False
2025-07-15 20:41:38,348 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.usda -> '(Checkbox_12)' = False
2025-07-15 20:41:38,348 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_13)
2025-07-15 20:41:38,348 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:41:38,348 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_13) existing appearance keys: ['/Checkbox_13', '/Off']
2025-07-15 20:41:38,348 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_13) using field-specific on state: /Checkbox_13
2025-07-15 20:41:38,349 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_13) detected states - On: /Checkbox_13, Off: /Off
2025-07-15 20:41:38,349 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_13, Off: /Off
2025-07-15 20:41:38,349 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_13) to UNCHECKED (/Off)
2025-07-15 20:41:38,349 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_13) to unchecked
2025-07-15 20:41:38,349 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_13)' = False
2025-07-15 20:41:38,351 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.other -> '(Checkbox_13)' = False
2025-07-15 20:41:38,351 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_1)
2025-07-15 20:41:38,351 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:41:38,352 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_1) existing appearance keys: ['/Checkbox_1', '/Off']
2025-07-15 20:41:38,352 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_1) using field-specific on state: /Checkbox_1
2025-07-15 20:41:38,352 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_1) detected states - On: /Checkbox_1, Off: /Off
2025-07-15 20:41:38,352 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_1, Off: /Off
2025-07-15 20:41:38,352 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_1) to CHECKED (/Checkbox_1)
2025-07-15 20:41:38,352 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_1) to checked
2025-07-15 20:41:38,353 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_1)' = True
2025-07-15 20:41:38,354 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.as_is_condition -> '(Checkbox_1)' = True
2025-07-15 20:41:38,354 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_2)
2025-07-15 20:41:38,354 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:41:38,355 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_2) existing appearance keys: ['/Checkbox_2', '/Off']
2025-07-15 20:41:38,355 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_2) using field-specific on state: /Checkbox_2
2025-07-15 20:41:38,355 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_2) detected states - On: /Checkbox_2, Off: /Off
2025-07-15 20:41:38,355 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_2, Off: /Off
2025-07-15 20:41:38,355 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_2) to UNCHECKED (/Off)
2025-07-15 20:41:38,355 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_2) to unchecked
2025-07-15 20:41:38,355 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_2)' = False
2025-07-15 20:41:38,357 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.seller_repairs -> '(Checkbox_2)' = False
2025-07-15 20:41:38,357 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_3)
2025-07-15 20:41:38,358 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:41:38,358 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_3) existing appearance keys: ['/Checkbox_3', '/Off']
2025-07-15 20:41:38,358 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_3) using field-specific on state: /Checkbox_3
2025-07-15 20:41:38,359 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_3) detected states - On: /Checkbox_3, Off: /Off
2025-07-15 20:41:38,359 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_3, Off: /Off
2025-07-15 20:41:38,359 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_3) to CHECKED (/Checkbox_3)
2025-07-15 20:41:38,359 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_3) to checked
2025-07-15 20:41:38,359 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_3)' = True
2025-07-15 20:41:38,361 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.buyer_inspection -> '(Checkbox_3)' = True
2025-07-15 20:41:38,361 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_4)
2025-07-15 20:41:38,361 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:41:38,361 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_4) existing appearance keys: ['/Checkbox_4', '/Off']
2025-07-15 20:41:38,361 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_4) using field-specific on state: /Checkbox_4
2025-07-15 20:41:38,361 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_4) detected states - On: /Checkbox_4, Off: /Off
2025-07-15 20:41:38,361 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_4, Off: /Off
2025-07-15 20:41:38,361 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_4) to CHECKED (/Checkbox_4)
2025-07-15 20:41:38,361 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_4) to checked
2025-07-15 20:41:38,362 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_4)' = True
2025-07-15 20:41:38,364 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.professional_inspection -> '(Checkbox_4)' = True
2025-07-15 20:41:38,364 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_5)
2025-07-15 20:41:38,364 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:41:38,364 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_5) existing appearance keys: ['/Checkbox_5', '/Off']
2025-07-15 20:41:38,364 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_5) using field-specific on state: /Checkbox_5
2025-07-15 20:41:38,364 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_5) detected states - On: /Checkbox_5, Off: /Off
2025-07-15 20:41:38,364 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_5, Off: /Off
2025-07-15 20:41:38,365 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_5) to UNCHECKED (/Off)
2025-07-15 20:41:38,365 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_5) to unchecked
2025-07-15 20:41:38,365 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_5)' = False
2025-07-15 20:41:38,366 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.termite_inspection -> '(Checkbox_5)' = False
2025-07-15 20:41:38,367 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_6)
2025-07-15 20:41:38,367 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:41:38,367 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_6) existing appearance keys: ['/Checkbox_6', '/Off']
2025-07-15 20:41:38,367 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_6) using field-specific on state: /Checkbox_6
2025-07-15 20:41:38,368 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_6) detected states - On: /Checkbox_6, Off: /Off
2025-07-15 20:41:38,368 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_6, Off: /Off
2025-07-15 20:41:38,368 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_6) to CHECKED (/Checkbox_6)
2025-07-15 20:41:38,368 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_6) to checked
2025-07-15 20:41:38,368 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_6)' = True
2025-07-15 20:41:38,370 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.roof_inspection -> '(Checkbox_6)' = True
2025-07-15 20:41:38,371 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_28)
2025-07-15 20:41:38,371 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:41:38,371 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_28) existing appearance keys: ['/Checkbox_28', '/Off']
2025-07-15 20:41:38,371 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_28) using field-specific on state: /Checkbox_28
2025-07-15 20:41:38,372 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_28) detected states - On: /Checkbox_28, Off: /Off
2025-07-15 20:41:38,372 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_28, Off: /Off
2025-07-15 20:41:38,372 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_28) to CHECKED (/Checkbox_28)
2025-07-15 20:41:38,372 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_28) to checked
2025-07-15 20:41:38,372 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_28)' = True
2025-07-15 20:41:38,374 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.refrigerator -> '(Checkbox_28)' = True
2025-07-15 20:41:38,375 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_29)
2025-07-15 20:41:38,375 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:41:38,375 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_29) existing appearance keys: ['/Checkbox_29', '/Off']
2025-07-15 20:41:38,375 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_29) using field-specific on state: /Checkbox_29
2025-07-15 20:41:38,375 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_29) detected states - On: /Checkbox_29, Off: /Off
2025-07-15 20:41:38,376 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_29, Off: /Off
2025-07-15 20:41:38,376 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_29) to UNCHECKED (/Off)
2025-07-15 20:41:38,376 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_29) to unchecked
2025-07-15 20:41:38,376 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_29)' = False
2025-07-15 20:41:38,377 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.washer -> '(Checkbox_29)' = False
2025-07-15 20:41:38,378 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_30)
2025-07-15 20:41:38,378 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:41:38,378 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_30) existing appearance keys: ['/Checkbox_30', '/Off']
2025-07-15 20:41:38,378 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_30) using field-specific on state: /Checkbox_30
2025-07-15 20:41:38,378 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_30) detected states - On: /Checkbox_30, Off: /Off
2025-07-15 20:41:38,378 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_30, Off: /Off
2025-07-15 20:41:38,378 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_30) to CHECKED (/Checkbox_30)
2025-07-15 20:41:38,379 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_30) to checked
2025-07-15 20:41:38,379 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_30)' = True
2025-07-15 20:41:38,380 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.dryer -> '(Checkbox_30)' = True
2025-07-15 20:41:38,381 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_31)
2025-07-15 20:41:38,381 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:41:38,381 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_31) existing appearance keys: ['/Checkbox_31', '/Off']
2025-07-15 20:41:38,381 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_31) using field-specific on state: /Checkbox_31
2025-07-15 20:41:38,382 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_31) detected states - On: /Checkbox_31, Off: /Off
2025-07-15 20:41:38,382 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_31, Off: /Off
2025-07-15 20:41:38,382 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_31) to CHECKED (/Checkbox_31)
2025-07-15 20:41:38,382 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_31) to checked
2025-07-15 20:41:38,382 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_31)' = True
2025-07-15 20:41:38,383 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.dishwasher -> '(Checkbox_31)' = True
2025-07-15 20:41:38,384 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_32)
2025-07-15 20:41:38,384 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:41:38,384 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_32) existing appearance keys: ['/Checkbox_32', '/Off']
2025-07-15 20:41:38,384 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_32) using field-specific on state: /Checkbox_32
2025-07-15 20:41:38,384 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_32) detected states - On: /Checkbox_32, Off: /Off
2025-07-15 20:41:38,384 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_32, Off: /Off
2025-07-15 20:41:38,385 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_32) to UNCHECKED (/Off)
2025-07-15 20:41:38,385 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_32) to unchecked
2025-07-15 20:41:38,385 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_32)' = False
2025-07-15 20:41:38,386 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.microwave -> '(Checkbox_32)' = False
2025-07-15 20:41:38,387 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_33)
2025-07-15 20:41:38,387 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:41:38,388 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_33) existing appearance keys: ['/Checkbox_33', '/Off']
2025-07-15 20:41:38,388 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_33) using field-specific on state: /Checkbox_33
2025-07-15 20:41:38,388 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_33) detected states - On: /Checkbox_33, Off: /Off
2025-07-15 20:41:38,388 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_33, Off: /Off
2025-07-15 20:41:38,388 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_33) to CHECKED (/Checkbox_33)
2025-07-15 20:41:38,388 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_33) to checked
2025-07-15 20:41:38,388 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_33)' = True
2025-07-15 20:41:38,389 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.oven_range -> '(Checkbox_33)' = True
2025-07-15 20:41:38,456 - __main__ - INFO - fill_pdf:1103 - PDF generated successfully. Processed: 21, Failed: 0
2025-07-15 20:41:38,456 - __main__ - INFO - fill_pdf:1266 - PDF generated successfully. Fields processed: 21, Failed: 0
2025-07-15 20:41:38,457 - __main__ - INFO - fill_pdf:1302 - Checkbox debug mode disabled
2025-07-15 20:41:38,457 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:41:38] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 20:42:01,448 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/comprehensive_checkbox_fix.py', reloading
2025-07-15 20:42:01,556 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 20:42:02,061 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:42:02,061 - __main__ - INFO - _load_field_mapping:715 - Loaded field mapping with 115 fields
2025-07-15 20:42:02,062 - __main__ - WARNING - _load_field_mapping_guide:733 - Field mapping guide not found
2025-07-15 20:42:02,062 - __main__ - INFO - _load_pdf_fields_info:745 - Loaded 188 PDF field definitions
2025-07-15 20:42:02,080 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 20:42:02,095 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 20:42:56,658 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/comprehensive_checkbox_fix.py', reloading
2025-07-15 20:42:56,733 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 20:42:57,560 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 20:42:57,562 - __main__ - INFO - _load_field_mapping:715 - Loaded field mapping with 115 fields
2025-07-15 20:42:57,562 - __main__ - WARNING - _load_field_mapping_guide:733 - Field mapping guide not found
2025-07-15 20:42:57,563 - __main__ - INFO - _load_pdf_fields_info:745 - Loaded 188 PDF field definitions
2025-07-15 20:42:57,598 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 20:42:57,622 - werkzeug - INFO - _log:187 -  * Debugger PIN: 124-219-522
2025-07-15 20:43:17,706 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:43:17] "GET /debug-checkboxes HTTP/1.1" 200 -
2025-07-15 20:43:17,713 - __main__ - INFO - fill_pdf:1248 - Received PDF fill request with 6 fields
2025-07-15 20:43:17,714 - __main__ - INFO - fill_pdf:1253 - Checkbox debug mode enabled
2025-07-15 20:43:17,772 - __main__ - INFO - fill_pdf:1066 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 20:43:17,773 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_1)' = Enhanced Test Seller
2025-07-15 20:43:17,775 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: seller_name -> '(Text_1)' = Enhanced Test Seller
2025-07-15 20:43:17,775 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_2)' = Enhanced Test Buyer
2025-07-15 20:43:17,777 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: buyer_name -> '(Text_2)' = Enhanced Test Buyer
2025-07-15 20:43:17,778 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Number_1)' = 850000
2025-07-15 20:43:17,780 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: purchase_price -> '(Number_1)' = 850000
2025-07-15 20:43:17,780 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_8)
2025-07-15 20:43:17,780 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:43:17,781 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_8) existing appearance keys: ['/Checkbox_8', '/Off']
2025-07-15 20:43:17,781 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_8) using field-specific on state: /Checkbox_8
2025-07-15 20:43:17,781 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_8) detected states - On: /Checkbox_8, Off: /Off
2025-07-15 20:43:17,783 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_8, Off: /Off
2025-07-15 20:43:17,783 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_8) to CHECKED (/Checkbox_8)
2025-07-15 20:43:17,784 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_8) to checked
2025-07-15 20:43:17,784 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_8)' = True
2025-07-15 20:43:17,786 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.cash -> '(Checkbox_8)' = True
2025-07-15 20:43:17,787 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_9)
2025-07-15 20:43:17,787 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:43:17,787 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_9) existing appearance keys: ['/Checkbox_9', '/Off']
2025-07-15 20:43:17,787 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_9) using field-specific on state: /Checkbox_9
2025-07-15 20:43:17,787 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_9) detected states - On: /Checkbox_9, Off: /Off
2025-07-15 20:43:17,787 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_9, Off: /Off
2025-07-15 20:43:17,787 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_9) to UNCHECKED (/Off)
2025-07-15 20:43:17,787 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_9) to unchecked
2025-07-15 20:43:17,788 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_9)' = False
2025-07-15 20:43:17,789 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.conventional -> '(Checkbox_9)' = False
2025-07-15 20:43:17,789 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_10)
2025-07-15 20:43:17,790 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:43:17,791 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_10) existing appearance keys: ['/Checkbox_10', '/Off']
2025-07-15 20:43:17,791 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_10) using field-specific on state: /Checkbox_10
2025-07-15 20:43:17,791 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_10) detected states - On: /Checkbox_10, Off: /Off
2025-07-15 20:43:17,791 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_10, Off: /Off
2025-07-15 20:43:17,791 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_10) to CHECKED (/Checkbox_10)
2025-07-15 20:43:17,791 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_10) to checked
2025-07-15 20:43:17,791 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_10)' = True
2025-07-15 20:43:17,793 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.fha -> '(Checkbox_10)' = True
2025-07-15 20:43:17,793 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_11)
2025-07-15 20:43:17,793 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:43:17,794 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_11) existing appearance keys: ['/Checkbox_11', '/Off']
2025-07-15 20:43:17,794 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_11) using field-specific on state: /Checkbox_11
2025-07-15 20:43:17,794 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_11) detected states - On: /Checkbox_11, Off: /Off
2025-07-15 20:43:17,794 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_11, Off: /Off
2025-07-15 20:43:17,794 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_11) to UNCHECKED (/Off)
2025-07-15 20:43:17,794 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_11) to unchecked
2025-07-15 20:43:17,794 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_11)' = False
2025-07-15 20:43:17,796 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.va -> '(Checkbox_11)' = False
2025-07-15 20:43:17,797 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_12)
2025-07-15 20:43:17,797 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:43:17,797 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_12) existing appearance keys: ['/Checkbox_12', '/Off']
2025-07-15 20:43:17,797 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_12) using field-specific on state: /Checkbox_12
2025-07-15 20:43:17,797 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_12) detected states - On: /Checkbox_12, Off: /Off
2025-07-15 20:43:17,799 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_12, Off: /Off
2025-07-15 20:43:17,800 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_12) to UNCHECKED (/Off)
2025-07-15 20:43:17,800 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_12) to unchecked
2025-07-15 20:43:17,800 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_12)' = False
2025-07-15 20:43:17,804 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.usda -> '(Checkbox_12)' = False
2025-07-15 20:43:17,805 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_13)
2025-07-15 20:43:17,805 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:43:17,805 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_13) existing appearance keys: ['/Checkbox_13', '/Off']
2025-07-15 20:43:17,805 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_13) using field-specific on state: /Checkbox_13
2025-07-15 20:43:17,805 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_13) detected states - On: /Checkbox_13, Off: /Off
2025-07-15 20:43:17,805 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_13, Off: /Off
2025-07-15 20:43:17,806 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_13) to UNCHECKED (/Off)
2025-07-15 20:43:17,806 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_13) to unchecked
2025-07-15 20:43:17,806 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_13)' = False
2025-07-15 20:43:17,808 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.other -> '(Checkbox_13)' = False
2025-07-15 20:43:17,808 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_1)
2025-07-15 20:43:17,808 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:43:17,809 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_1) existing appearance keys: ['/Checkbox_1', '/Off']
2025-07-15 20:43:17,809 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_1) using field-specific on state: /Checkbox_1
2025-07-15 20:43:17,809 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_1) detected states - On: /Checkbox_1, Off: /Off
2025-07-15 20:43:17,809 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_1, Off: /Off
2025-07-15 20:43:17,809 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_1) to CHECKED (/Checkbox_1)
2025-07-15 20:43:17,809 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_1) to checked
2025-07-15 20:43:17,809 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_1)' = True
2025-07-15 20:43:17,812 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.as_is_condition -> '(Checkbox_1)' = True
2025-07-15 20:43:17,812 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_2)
2025-07-15 20:43:17,812 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:43:17,813 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_2) existing appearance keys: ['/Checkbox_2', '/Off']
2025-07-15 20:43:17,813 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_2) using field-specific on state: /Checkbox_2
2025-07-15 20:43:17,813 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_2) detected states - On: /Checkbox_2, Off: /Off
2025-07-15 20:43:17,813 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_2, Off: /Off
2025-07-15 20:43:17,814 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_2) to UNCHECKED (/Off)
2025-07-15 20:43:17,814 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_2) to unchecked
2025-07-15 20:43:17,819 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_2)' = False
2025-07-15 20:43:17,821 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.seller_repairs -> '(Checkbox_2)' = False
2025-07-15 20:43:17,822 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_3)
2025-07-15 20:43:17,822 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:43:17,822 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_3) existing appearance keys: ['/Checkbox_3', '/Off']
2025-07-15 20:43:17,824 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_3) using field-specific on state: /Checkbox_3
2025-07-15 20:43:17,824 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_3) detected states - On: /Checkbox_3, Off: /Off
2025-07-15 20:43:17,824 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_3, Off: /Off
2025-07-15 20:43:17,825 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_3) to CHECKED (/Checkbox_3)
2025-07-15 20:43:17,825 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_3) to checked
2025-07-15 20:43:17,826 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_3)' = True
2025-07-15 20:43:17,839 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.buyer_inspection -> '(Checkbox_3)' = True
2025-07-15 20:43:17,840 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_4)
2025-07-15 20:43:17,841 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:43:17,842 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_4) existing appearance keys: ['/Checkbox_4', '/Off']
2025-07-15 20:43:17,842 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_4) using field-specific on state: /Checkbox_4
2025-07-15 20:43:17,842 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_4) detected states - On: /Checkbox_4, Off: /Off
2025-07-15 20:43:17,842 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_4, Off: /Off
2025-07-15 20:43:17,843 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_4) to CHECKED (/Checkbox_4)
2025-07-15 20:43:17,843 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_4) to checked
2025-07-15 20:43:17,843 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_4)' = True
2025-07-15 20:43:17,845 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.professional_inspection -> '(Checkbox_4)' = True
2025-07-15 20:43:17,845 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_5)
2025-07-15 20:43:17,846 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:43:17,846 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_5) existing appearance keys: ['/Checkbox_5', '/Off']
2025-07-15 20:43:17,846 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_5) using field-specific on state: /Checkbox_5
2025-07-15 20:43:17,846 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_5) detected states - On: /Checkbox_5, Off: /Off
2025-07-15 20:43:17,846 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_5, Off: /Off
2025-07-15 20:43:17,846 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_5) to UNCHECKED (/Off)
2025-07-15 20:43:17,846 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_5) to unchecked
2025-07-15 20:43:17,858 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_5)' = False
2025-07-15 20:43:17,938 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.termite_inspection -> '(Checkbox_5)' = False
2025-07-15 20:43:17,940 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_6)
2025-07-15 20:43:17,940 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:43:17,941 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_6) existing appearance keys: ['/Checkbox_6', '/Off']
2025-07-15 20:43:17,941 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_6) using field-specific on state: /Checkbox_6
2025-07-15 20:43:17,941 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_6) detected states - On: /Checkbox_6, Off: /Off
2025-07-15 20:43:17,941 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_6, Off: /Off
2025-07-15 20:43:17,943 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_6) to CHECKED (/Checkbox_6)
2025-07-15 20:43:17,945 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_6) to checked
2025-07-15 20:43:17,946 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_6)' = True
2025-07-15 20:43:17,951 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.roof_inspection -> '(Checkbox_6)' = True
2025-07-15 20:43:17,954 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_28)
2025-07-15 20:43:17,955 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:43:17,955 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_28) existing appearance keys: ['/Checkbox_28', '/Off']
2025-07-15 20:43:17,956 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_28) using field-specific on state: /Checkbox_28
2025-07-15 20:43:17,956 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_28) detected states - On: /Checkbox_28, Off: /Off
2025-07-15 20:43:17,957 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_28, Off: /Off
2025-07-15 20:43:17,957 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_28) to CHECKED (/Checkbox_28)
2025-07-15 20:43:17,957 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_28) to checked
2025-07-15 20:43:17,958 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_28)' = True
2025-07-15 20:43:17,959 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.refrigerator -> '(Checkbox_28)' = True
2025-07-15 20:43:17,988 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_29)
2025-07-15 20:43:17,988 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:43:17,988 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_29) existing appearance keys: ['/Checkbox_29', '/Off']
2025-07-15 20:43:17,988 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_29) using field-specific on state: /Checkbox_29
2025-07-15 20:43:17,988 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_29) detected states - On: /Checkbox_29, Off: /Off
2025-07-15 20:43:17,988 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_29, Off: /Off
2025-07-15 20:43:17,989 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_29) to UNCHECKED (/Off)
2025-07-15 20:43:17,990 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_29) to unchecked
2025-07-15 20:43:17,990 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_29)' = False
2025-07-15 20:43:17,993 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.washer -> '(Checkbox_29)' = False
2025-07-15 20:43:17,994 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_30)
2025-07-15 20:43:17,995 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:43:17,995 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_30) existing appearance keys: ['/Checkbox_30', '/Off']
2025-07-15 20:43:17,995 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_30) using field-specific on state: /Checkbox_30
2025-07-15 20:43:17,995 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_30) detected states - On: /Checkbox_30, Off: /Off
2025-07-15 20:43:17,996 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_30, Off: /Off
2025-07-15 20:43:17,996 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_30) to CHECKED (/Checkbox_30)
2025-07-15 20:43:17,996 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_30) to checked
2025-07-15 20:43:17,996 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_30)' = True
2025-07-15 20:43:17,998 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.dryer -> '(Checkbox_30)' = True
2025-07-15 20:43:18,004 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_31)
2025-07-15 20:43:18,004 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:43:18,004 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_31) existing appearance keys: ['/Checkbox_31', '/Off']
2025-07-15 20:43:18,005 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_31) using field-specific on state: /Checkbox_31
2025-07-15 20:43:18,005 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_31) detected states - On: /Checkbox_31, Off: /Off
2025-07-15 20:43:18,005 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_31, Off: /Off
2025-07-15 20:43:18,005 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_31) to CHECKED (/Checkbox_31)
2025-07-15 20:43:18,005 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_31) to checked
2025-07-15 20:43:18,005 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_31)' = True
2025-07-15 20:43:18,007 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.dishwasher -> '(Checkbox_31)' = True
2025-07-15 20:43:18,008 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_32)
2025-07-15 20:43:18,008 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:43:18,009 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_32) existing appearance keys: ['/Checkbox_32', '/Off']
2025-07-15 20:43:18,010 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_32) using field-specific on state: /Checkbox_32
2025-07-15 20:43:18,010 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_32) detected states - On: /Checkbox_32, Off: /Off
2025-07-15 20:43:18,010 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_32, Off: /Off
2025-07-15 20:43:18,011 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_32) to UNCHECKED (/Off)
2025-07-15 20:43:18,011 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_32) to unchecked
2025-07-15 20:43:18,011 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_32)' = False
2025-07-15 20:43:18,013 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.microwave -> '(Checkbox_32)' = False
2025-07-15 20:43:18,014 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_33)
2025-07-15 20:43:18,014 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:43:18,015 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_33) existing appearance keys: ['/Checkbox_33', '/Off']
2025-07-15 20:43:18,015 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_33) using field-specific on state: /Checkbox_33
2025-07-15 20:43:18,015 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_33) detected states - On: /Checkbox_33, Off: /Off
2025-07-15 20:43:18,015 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_33, Off: /Off
2025-07-15 20:43:18,015 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_33) to CHECKED (/Checkbox_33)
2025-07-15 20:43:18,015 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_33) to checked
2025-07-15 20:43:18,015 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_33)' = True
2025-07-15 20:43:18,018 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.oven_range -> '(Checkbox_33)' = True
2025-07-15 20:43:18,101 - __main__ - INFO - fill_pdf:1103 - PDF generated successfully. Processed: 21, Failed: 0
2025-07-15 20:43:18,102 - __main__ - INFO - fill_pdf:1266 - PDF generated successfully. Fields processed: 21, Failed: 0
2025-07-15 20:43:18,102 - __main__ - INFO - fill_pdf:1302 - Checkbox debug mode disabled
2025-07-15 20:43:18,102 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:43:18] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 20:52:33,561 - __main__ - INFO - fill_pdf:1248 - Received PDF fill request with 7 fields
2025-07-15 20:52:33,568 - __main__ - INFO - fill_pdf:1253 - Checkbox debug mode enabled
2025-07-15 20:52:33,669 - __main__ - INFO - fill_pdf:1066 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 20:52:33,670 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_1)' = John Smith
2025-07-15 20:52:33,672 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: seller_name -> '(Text_1)' = John Smith
2025-07-15 20:52:33,672 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_2)' = Jane Doe
2025-07-15 20:52:33,674 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: buyer_name -> '(Text_2)' = Jane Doe
2025-07-15 20:52:33,675 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Number_1)' = 750000
2025-07-15 20:52:33,680 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: purchase_price -> '(Number_1)' = 750000
2025-07-15 20:52:33,680 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_3)' = 123 Main Street, Anytown, ST 12345
2025-07-15 20:52:33,683 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: property_address -> '(Text_3)' = 123 Main Street, Anytown, ST 12345
2025-07-15 20:52:33,684 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_8)
2025-07-15 20:52:33,684 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:52:33,684 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_8) existing appearance keys: ['/Checkbox_8', '/Off']
2025-07-15 20:52:33,684 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_8) using field-specific on state: /Checkbox_8
2025-07-15 20:52:33,684 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_8) detected states - On: /Checkbox_8, Off: /Off
2025-07-15 20:52:33,685 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_8, Off: /Off
2025-07-15 20:52:33,685 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_8) to CHECKED (/Checkbox_8)
2025-07-15 20:52:33,685 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_8) to checked
2025-07-15 20:52:33,685 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_8)' = True
2025-07-15 20:52:33,687 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.cash -> '(Checkbox_8)' = True
2025-07-15 20:52:33,687 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_9)
2025-07-15 20:52:33,688 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:52:33,688 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_9) existing appearance keys: ['/Checkbox_9', '/Off']
2025-07-15 20:52:33,688 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_9) using field-specific on state: /Checkbox_9
2025-07-15 20:52:33,689 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_9) detected states - On: /Checkbox_9, Off: /Off
2025-07-15 20:52:33,689 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_9, Off: /Off
2025-07-15 20:52:33,689 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_9) to UNCHECKED (/Off)
2025-07-15 20:52:33,689 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_9) to unchecked
2025-07-15 20:52:33,690 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_9)' = False
2025-07-15 20:52:33,692 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.conventional -> '(Checkbox_9)' = False
2025-07-15 20:52:33,693 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_10)
2025-07-15 20:52:33,693 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:52:33,693 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_10) existing appearance keys: ['/Checkbox_10', '/Off']
2025-07-15 20:52:33,693 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_10) using field-specific on state: /Checkbox_10
2025-07-15 20:52:33,693 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_10) detected states - On: /Checkbox_10, Off: /Off
2025-07-15 20:52:33,693 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_10, Off: /Off
2025-07-15 20:52:33,693 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_10) to CHECKED (/Checkbox_10)
2025-07-15 20:52:33,694 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_10) to checked
2025-07-15 20:52:33,695 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_10)' = True
2025-07-15 20:52:33,698 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.fha -> '(Checkbox_10)' = True
2025-07-15 20:52:33,698 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_11)
2025-07-15 20:52:33,698 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:52:33,699 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_11) existing appearance keys: ['/Checkbox_11', '/Off']
2025-07-15 20:52:33,699 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_11) using field-specific on state: /Checkbox_11
2025-07-15 20:52:33,699 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_11) detected states - On: /Checkbox_11, Off: /Off
2025-07-15 20:52:33,699 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_11, Off: /Off
2025-07-15 20:52:33,700 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_11) to UNCHECKED (/Off)
2025-07-15 20:52:33,700 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_11) to unchecked
2025-07-15 20:52:33,700 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_11)' = False
2025-07-15 20:52:33,702 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.va -> '(Checkbox_11)' = False
2025-07-15 20:52:33,702 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_12)
2025-07-15 20:52:33,702 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:52:33,703 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_12) existing appearance keys: ['/Checkbox_12', '/Off']
2025-07-15 20:52:33,703 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_12) using field-specific on state: /Checkbox_12
2025-07-15 20:52:33,703 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_12) detected states - On: /Checkbox_12, Off: /Off
2025-07-15 20:52:33,703 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_12, Off: /Off
2025-07-15 20:52:33,703 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_12) to UNCHECKED (/Off)
2025-07-15 20:52:33,703 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_12) to unchecked
2025-07-15 20:52:33,703 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_12)' = False
2025-07-15 20:52:33,706 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.usda -> '(Checkbox_12)' = False
2025-07-15 20:52:33,707 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_13)
2025-07-15 20:52:33,707 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:52:33,708 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_13) existing appearance keys: ['/Checkbox_13', '/Off']
2025-07-15 20:52:33,708 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_13) using field-specific on state: /Checkbox_13
2025-07-15 20:52:33,708 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_13) detected states - On: /Checkbox_13, Off: /Off
2025-07-15 20:52:33,708 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_13, Off: /Off
2025-07-15 20:52:33,708 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_13) to UNCHECKED (/Off)
2025-07-15 20:52:33,708 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_13) to unchecked
2025-07-15 20:52:33,709 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_13)' = False
2025-07-15 20:52:33,712 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.other -> '(Checkbox_13)' = False
2025-07-15 20:52:33,712 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_1)
2025-07-15 20:52:33,713 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:52:33,713 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_1) existing appearance keys: ['/Checkbox_1', '/Off']
2025-07-15 20:52:33,713 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_1) using field-specific on state: /Checkbox_1
2025-07-15 20:52:33,713 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_1) detected states - On: /Checkbox_1, Off: /Off
2025-07-15 20:52:33,714 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_1, Off: /Off
2025-07-15 20:52:33,714 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_1) to CHECKED (/Checkbox_1)
2025-07-15 20:52:33,714 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_1) to checked
2025-07-15 20:52:33,714 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_1)' = True
2025-07-15 20:52:33,716 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.as_is_condition -> '(Checkbox_1)' = True
2025-07-15 20:52:33,717 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_2)
2025-07-15 20:52:33,717 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:52:33,717 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_2) existing appearance keys: ['/Checkbox_2', '/Off']
2025-07-15 20:52:33,718 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_2) using field-specific on state: /Checkbox_2
2025-07-15 20:52:33,718 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_2) detected states - On: /Checkbox_2, Off: /Off
2025-07-15 20:52:33,718 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_2, Off: /Off
2025-07-15 20:52:33,718 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_2) to UNCHECKED (/Off)
2025-07-15 20:52:33,718 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_2) to unchecked
2025-07-15 20:52:33,718 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_2)' = False
2025-07-15 20:52:33,720 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.seller_repairs -> '(Checkbox_2)' = False
2025-07-15 20:52:33,720 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_3)
2025-07-15 20:52:33,720 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:52:33,721 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_3) existing appearance keys: ['/Checkbox_3', '/Off']
2025-07-15 20:52:33,721 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_3) using field-specific on state: /Checkbox_3
2025-07-15 20:52:33,721 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_3) detected states - On: /Checkbox_3, Off: /Off
2025-07-15 20:52:33,721 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_3, Off: /Off
2025-07-15 20:52:33,721 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_3) to CHECKED (/Checkbox_3)
2025-07-15 20:52:33,721 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_3) to checked
2025-07-15 20:52:33,721 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_3)' = True
2025-07-15 20:52:33,724 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.buyer_inspection -> '(Checkbox_3)' = True
2025-07-15 20:52:33,725 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_4)
2025-07-15 20:52:33,725 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:52:33,725 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_4) existing appearance keys: ['/Checkbox_4', '/Off']
2025-07-15 20:52:33,725 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_4) using field-specific on state: /Checkbox_4
2025-07-15 20:52:33,725 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_4) detected states - On: /Checkbox_4, Off: /Off
2025-07-15 20:52:33,726 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_4, Off: /Off
2025-07-15 20:52:33,726 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_4) to CHECKED (/Checkbox_4)
2025-07-15 20:52:33,726 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_4) to checked
2025-07-15 20:52:33,726 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_4)' = True
2025-07-15 20:52:33,729 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.professional_inspection -> '(Checkbox_4)' = True
2025-07-15 20:52:33,730 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_5)
2025-07-15 20:52:33,731 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:52:33,731 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_5) existing appearance keys: ['/Checkbox_5', '/Off']
2025-07-15 20:52:33,731 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_5) using field-specific on state: /Checkbox_5
2025-07-15 20:52:33,731 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_5) detected states - On: /Checkbox_5, Off: /Off
2025-07-15 20:52:33,731 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_5, Off: /Off
2025-07-15 20:52:33,732 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_5) to UNCHECKED (/Off)
2025-07-15 20:52:33,732 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_5) to unchecked
2025-07-15 20:52:33,732 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_5)' = False
2025-07-15 20:52:33,734 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.termite_inspection -> '(Checkbox_5)' = False
2025-07-15 20:52:33,734 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_6)
2025-07-15 20:52:33,734 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:52:33,734 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_6) existing appearance keys: ['/Checkbox_6', '/Off']
2025-07-15 20:52:33,734 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_6) using field-specific on state: /Checkbox_6
2025-07-15 20:52:33,735 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_6) detected states - On: /Checkbox_6, Off: /Off
2025-07-15 20:52:33,735 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_6, Off: /Off
2025-07-15 20:52:33,735 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_6) to CHECKED (/Checkbox_6)
2025-07-15 20:52:33,735 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_6) to checked
2025-07-15 20:52:33,735 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_6)' = True
2025-07-15 20:52:33,738 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.roof_inspection -> '(Checkbox_6)' = True
2025-07-15 20:52:33,739 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_28)
2025-07-15 20:52:33,739 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:52:33,739 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_28) existing appearance keys: ['/Checkbox_28', '/Off']
2025-07-15 20:52:33,739 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_28) using field-specific on state: /Checkbox_28
2025-07-15 20:52:33,739 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_28) detected states - On: /Checkbox_28, Off: /Off
2025-07-15 20:52:33,740 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_28, Off: /Off
2025-07-15 20:52:33,740 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_28) to CHECKED (/Checkbox_28)
2025-07-15 20:52:33,740 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_28) to checked
2025-07-15 20:52:33,740 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_28)' = True
2025-07-15 20:52:33,742 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.refrigerator -> '(Checkbox_28)' = True
2025-07-15 20:52:33,742 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_29)
2025-07-15 20:52:33,742 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:52:33,744 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_29) existing appearance keys: ['/Checkbox_29', '/Off']
2025-07-15 20:52:33,744 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_29) using field-specific on state: /Checkbox_29
2025-07-15 20:52:33,745 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_29) detected states - On: /Checkbox_29, Off: /Off
2025-07-15 20:52:33,745 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_29, Off: /Off
2025-07-15 20:52:33,745 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_29) to UNCHECKED (/Off)
2025-07-15 20:52:33,745 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_29) to unchecked
2025-07-15 20:52:33,745 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_29)' = False
2025-07-15 20:52:33,747 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.washer -> '(Checkbox_29)' = False
2025-07-15 20:52:33,748 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_30)
2025-07-15 20:52:33,748 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:52:33,748 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_30) existing appearance keys: ['/Checkbox_30', '/Off']
2025-07-15 20:52:33,749 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_30) using field-specific on state: /Checkbox_30
2025-07-15 20:52:33,749 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_30) detected states - On: /Checkbox_30, Off: /Off
2025-07-15 20:52:33,749 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_30, Off: /Off
2025-07-15 20:52:33,749 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_30) to CHECKED (/Checkbox_30)
2025-07-15 20:52:33,749 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_30) to checked
2025-07-15 20:52:33,750 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_30)' = True
2025-07-15 20:52:33,752 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.dryer -> '(Checkbox_30)' = True
2025-07-15 20:52:33,757 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_31)
2025-07-15 20:52:33,757 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:52:33,758 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_31) existing appearance keys: ['/Checkbox_31', '/Off']
2025-07-15 20:52:33,758 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_31) using field-specific on state: /Checkbox_31
2025-07-15 20:52:33,758 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_31) detected states - On: /Checkbox_31, Off: /Off
2025-07-15 20:52:33,758 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_31, Off: /Off
2025-07-15 20:52:33,758 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_31) to CHECKED (/Checkbox_31)
2025-07-15 20:52:33,758 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_31) to checked
2025-07-15 20:52:33,758 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_31)' = True
2025-07-15 20:52:33,761 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.dishwasher -> '(Checkbox_31)' = True
2025-07-15 20:52:33,763 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_32)
2025-07-15 20:52:33,763 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:52:33,763 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_32) existing appearance keys: ['/Checkbox_32', '/Off']
2025-07-15 20:52:33,765 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_32) using field-specific on state: /Checkbox_32
2025-07-15 20:52:33,766 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_32) detected states - On: /Checkbox_32, Off: /Off
2025-07-15 20:52:33,766 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_32, Off: /Off
2025-07-15 20:52:33,766 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_32) to UNCHECKED (/Off)
2025-07-15 20:52:33,767 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_32) to unchecked
2025-07-15 20:52:33,767 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_32)' = False
2025-07-15 20:52:33,769 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.microwave -> '(Checkbox_32)' = False
2025-07-15 20:52:33,769 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_33)
2025-07-15 20:52:33,770 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:52:33,770 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_33) existing appearance keys: ['/Checkbox_33', '/Off']
2025-07-15 20:52:33,770 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_33) using field-specific on state: /Checkbox_33
2025-07-15 20:52:33,770 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_33) detected states - On: /Checkbox_33, Off: /Off
2025-07-15 20:52:33,770 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_33, Off: /Off
2025-07-15 20:52:33,770 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_33) to CHECKED (/Checkbox_33)
2025-07-15 20:52:33,770 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_33) to checked
2025-07-15 20:52:33,771 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_33)' = True
2025-07-15 20:52:33,773 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.oven_range -> '(Checkbox_33)' = True
2025-07-15 20:52:33,884 - __main__ - INFO - fill_pdf:1103 - PDF generated successfully. Processed: 22, Failed: 0
2025-07-15 20:52:33,884 - __main__ - INFO - fill_pdf:1266 - PDF generated successfully. Fields processed: 22, Failed: 0
2025-07-15 20:52:33,885 - __main__ - INFO - fill_pdf:1302 - Checkbox debug mode disabled
2025-07-15 20:52:33,886 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:52:33] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 20:53:23,592 - __main__ - INFO - fill_pdf:1248 - Received PDF fill request with 6 fields
2025-07-15 20:53:23,597 - __main__ - INFO - fill_pdf:1253 - Checkbox debug mode enabled
2025-07-15 20:53:23,674 - __main__ - INFO - fill_pdf:1066 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 20:53:23,675 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_1)' = String Test Seller
2025-07-15 20:53:23,677 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: seller_name -> '(Text_1)' = String Test Seller
2025-07-15 20:53:23,677 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_2)' = String Test Buyer
2025-07-15 20:53:23,678 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: buyer_name -> '(Text_2)' = String Test Buyer
2025-07-15 20:53:23,679 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Number_1)' = 600000
2025-07-15 20:53:23,681 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: purchase_price -> '(Number_1)' = 600000
2025-07-15 20:53:23,682 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_8)
2025-07-15 20:53:23,682 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:53:23,682 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_8) existing appearance keys: ['/Checkbox_8', '/Off']
2025-07-15 20:53:23,682 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_8) using field-specific on state: /Checkbox_8
2025-07-15 20:53:23,682 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_8) detected states - On: /Checkbox_8, Off: /Off
2025-07-15 20:53:23,682 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_8, Off: /Off
2025-07-15 20:53:23,682 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_8) to CHECKED (/Checkbox_8)
2025-07-15 20:53:23,682 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_8) to checked
2025-07-15 20:53:23,682 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_8)' = True
2025-07-15 20:53:23,684 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.cash -> '(Checkbox_8)' = True
2025-07-15 20:53:23,684 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_9)
2025-07-15 20:53:23,684 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:23,685 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_9) existing appearance keys: ['/Checkbox_9', '/Off']
2025-07-15 20:53:23,685 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_9) using field-specific on state: /Checkbox_9
2025-07-15 20:53:23,685 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_9) detected states - On: /Checkbox_9, Off: /Off
2025-07-15 20:53:23,686 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_9, Off: /Off
2025-07-15 20:53:23,686 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_9) to UNCHECKED (/Off)
2025-07-15 20:53:23,686 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_9) to unchecked
2025-07-15 20:53:23,686 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_9)' = False
2025-07-15 20:53:23,689 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.conventional -> '(Checkbox_9)' = False
2025-07-15 20:53:23,689 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_10)
2025-07-15 20:53:23,689 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:53:23,690 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_10) existing appearance keys: ['/Checkbox_10', '/Off']
2025-07-15 20:53:23,690 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_10) using field-specific on state: /Checkbox_10
2025-07-15 20:53:23,690 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_10) detected states - On: /Checkbox_10, Off: /Off
2025-07-15 20:53:23,690 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_10, Off: /Off
2025-07-15 20:53:23,690 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_10) to CHECKED (/Checkbox_10)
2025-07-15 20:53:23,690 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_10) to checked
2025-07-15 20:53:23,690 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_10)' = True
2025-07-15 20:53:23,692 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.fha -> '(Checkbox_10)' = True
2025-07-15 20:53:23,692 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_11)
2025-07-15 20:53:23,692 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:23,693 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_11) existing appearance keys: ['/Checkbox_11', '/Off']
2025-07-15 20:53:23,693 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_11) using field-specific on state: /Checkbox_11
2025-07-15 20:53:23,693 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_11) detected states - On: /Checkbox_11, Off: /Off
2025-07-15 20:53:23,693 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_11, Off: /Off
2025-07-15 20:53:23,693 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_11) to UNCHECKED (/Off)
2025-07-15 20:53:23,693 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_11) to unchecked
2025-07-15 20:53:23,693 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_11)' = False
2025-07-15 20:53:23,694 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.va -> '(Checkbox_11)' = False
2025-07-15 20:53:23,695 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_1)
2025-07-15 20:53:23,695 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:53:23,696 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_1) existing appearance keys: ['/Checkbox_1', '/Off']
2025-07-15 20:53:23,697 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_1) using field-specific on state: /Checkbox_1
2025-07-15 20:53:23,697 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_1) detected states - On: /Checkbox_1, Off: /Off
2025-07-15 20:53:23,697 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_1, Off: /Off
2025-07-15 20:53:23,698 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_1) to CHECKED (/Checkbox_1)
2025-07-15 20:53:23,698 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_1) to checked
2025-07-15 20:53:23,698 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_1)' = True
2025-07-15 20:53:23,700 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.as_is_condition -> '(Checkbox_1)' = True
2025-07-15 20:53:23,700 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_3)
2025-07-15 20:53:23,700 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:23,701 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_3) existing appearance keys: ['/Checkbox_3', '/Off']
2025-07-15 20:53:23,701 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_3) using field-specific on state: /Checkbox_3
2025-07-15 20:53:23,701 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_3) detected states - On: /Checkbox_3, Off: /Off
2025-07-15 20:53:23,701 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_3, Off: /Off
2025-07-15 20:53:23,701 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_3) to UNCHECKED (/Off)
2025-07-15 20:53:23,701 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_3) to unchecked
2025-07-15 20:53:23,701 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_3)' = False
2025-07-15 20:53:23,703 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.buyer_inspection -> '(Checkbox_3)' = False
2025-07-15 20:53:23,703 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_4)
2025-07-15 20:53:23,703 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:53:23,704 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_4) existing appearance keys: ['/Checkbox_4', '/Off']
2025-07-15 20:53:23,704 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_4) using field-specific on state: /Checkbox_4
2025-07-15 20:53:23,704 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_4) detected states - On: /Checkbox_4, Off: /Off
2025-07-15 20:53:23,704 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_4, Off: /Off
2025-07-15 20:53:23,705 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_4) to CHECKED (/Checkbox_4)
2025-07-15 20:53:23,705 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_4) to checked
2025-07-15 20:53:23,705 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_4)' = True
2025-07-15 20:53:23,706 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.professional_inspection -> '(Checkbox_4)' = True
2025-07-15 20:53:23,707 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_28)
2025-07-15 20:53:23,707 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:23,708 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_28) existing appearance keys: ['/Checkbox_28', '/Off']
2025-07-15 20:53:23,708 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_28) using field-specific on state: /Checkbox_28
2025-07-15 20:53:23,709 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_28) detected states - On: /Checkbox_28, Off: /Off
2025-07-15 20:53:23,709 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_28, Off: /Off
2025-07-15 20:53:23,709 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_28) to UNCHECKED (/Off)
2025-07-15 20:53:23,709 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_28) to unchecked
2025-07-15 20:53:23,709 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_28)' = False
2025-07-15 20:53:23,711 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.refrigerator -> '(Checkbox_28)' = False
2025-07-15 20:53:23,712 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_29)
2025-07-15 20:53:23,712 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:23,713 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_29) existing appearance keys: ['/Checkbox_29', '/Off']
2025-07-15 20:53:23,713 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_29) using field-specific on state: /Checkbox_29
2025-07-15 20:53:23,713 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_29) detected states - On: /Checkbox_29, Off: /Off
2025-07-15 20:53:23,713 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_29, Off: /Off
2025-07-15 20:53:23,713 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_29) to UNCHECKED (/Off)
2025-07-15 20:53:23,713 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_29) to unchecked
2025-07-15 20:53:23,713 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_29)' = False
2025-07-15 20:53:23,714 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.washer -> '(Checkbox_29)' = False
2025-07-15 20:53:23,716 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_30)
2025-07-15 20:53:23,716 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:23,716 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_30) existing appearance keys: ['/Checkbox_30', '/Off']
2025-07-15 20:53:23,716 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_30) using field-specific on state: /Checkbox_30
2025-07-15 20:53:23,716 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_30) detected states - On: /Checkbox_30, Off: /Off
2025-07-15 20:53:23,717 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_30, Off: /Off
2025-07-15 20:53:23,717 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_30) to UNCHECKED (/Off)
2025-07-15 20:53:23,717 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_30) to unchecked
2025-07-15 20:53:23,717 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_30)' = False
2025-07-15 20:53:23,718 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.dryer -> '(Checkbox_30)' = False
2025-07-15 20:53:23,718 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_31)
2025-07-15 20:53:23,719 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:23,719 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_31) existing appearance keys: ['/Checkbox_31', '/Off']
2025-07-15 20:53:23,719 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_31) using field-specific on state: /Checkbox_31
2025-07-15 20:53:23,719 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_31) detected states - On: /Checkbox_31, Off: /Off
2025-07-15 20:53:23,719 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_31, Off: /Off
2025-07-15 20:53:23,720 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_31) to UNCHECKED (/Off)
2025-07-15 20:53:23,720 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_31) to unchecked
2025-07-15 20:53:23,720 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_31)' = False
2025-07-15 20:53:23,721 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.dishwasher -> '(Checkbox_31)' = False
2025-07-15 20:53:23,796 - __main__ - INFO - fill_pdf:1103 - PDF generated successfully. Processed: 14, Failed: 0
2025-07-15 20:53:23,797 - __main__ - INFO - fill_pdf:1266 - PDF generated successfully. Fields processed: 14, Failed: 0
2025-07-15 20:53:23,797 - __main__ - INFO - fill_pdf:1302 - Checkbox debug mode disabled
2025-07-15 20:53:23,797 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:53:23] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 20:53:33,774 - __main__ - INFO - fill_pdf:1248 - Received PDF fill request with 6 fields
2025-07-15 20:53:33,774 - __main__ - INFO - fill_pdf:1253 - Checkbox debug mode enabled
2025-07-15 20:53:33,853 - __main__ - INFO - fill_pdf:1066 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 20:53:33,855 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_1)' = Mixed Test Seller
2025-07-15 20:53:33,857 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: seller_name -> '(Text_1)' = Mixed Test Seller
2025-07-15 20:53:33,858 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_2)' = Mixed Test Buyer
2025-07-15 20:53:33,859 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: buyer_name -> '(Text_2)' = Mixed Test Buyer
2025-07-15 20:53:33,860 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Number_1)' = 800000
2025-07-15 20:53:33,863 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: purchase_price -> '(Number_1)' = 800000
2025-07-15 20:53:33,864 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_8)
2025-07-15 20:53:33,864 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:53:33,864 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_8) existing appearance keys: ['/Checkbox_8', '/Off']
2025-07-15 20:53:33,865 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_8) using field-specific on state: /Checkbox_8
2025-07-15 20:53:33,865 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_8) detected states - On: /Checkbox_8, Off: /Off
2025-07-15 20:53:33,865 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_8, Off: /Off
2025-07-15 20:53:33,865 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_8) to CHECKED (/Checkbox_8)
2025-07-15 20:53:33,865 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_8) to checked
2025-07-15 20:53:33,865 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_8)' = True
2025-07-15 20:53:33,868 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.cash -> '(Checkbox_8)' = True
2025-07-15 20:53:33,870 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_9)
2025-07-15 20:53:33,870 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:33,871 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_9) existing appearance keys: ['/Checkbox_9', '/Off']
2025-07-15 20:53:33,871 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_9) using field-specific on state: /Checkbox_9
2025-07-15 20:53:33,871 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_9) detected states - On: /Checkbox_9, Off: /Off
2025-07-15 20:53:33,872 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_9, Off: /Off
2025-07-15 20:53:33,873 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_9) to UNCHECKED (/Off)
2025-07-15 20:53:33,873 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_9) to unchecked
2025-07-15 20:53:33,873 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_9)' = False
2025-07-15 20:53:33,879 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.conventional -> '(Checkbox_9)' = False
2025-07-15 20:53:33,882 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_10)
2025-07-15 20:53:33,882 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:53:33,882 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_10) existing appearance keys: ['/Checkbox_10', '/Off']
2025-07-15 20:53:33,882 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_10) using field-specific on state: /Checkbox_10
2025-07-15 20:53:33,882 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_10) detected states - On: /Checkbox_10, Off: /Off
2025-07-15 20:53:33,882 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_10, Off: /Off
2025-07-15 20:53:33,883 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_10) to CHECKED (/Checkbox_10)
2025-07-15 20:53:33,884 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_10) to checked
2025-07-15 20:53:33,884 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_10)' = True
2025-07-15 20:53:33,889 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.fha -> '(Checkbox_10)' = True
2025-07-15 20:53:33,891 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_11)
2025-07-15 20:53:33,891 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:33,891 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_11) existing appearance keys: ['/Checkbox_11', '/Off']
2025-07-15 20:53:33,892 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_11) using field-specific on state: /Checkbox_11
2025-07-15 20:53:33,892 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_11) detected states - On: /Checkbox_11, Off: /Off
2025-07-15 20:53:33,892 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_11, Off: /Off
2025-07-15 20:53:33,893 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_11) to UNCHECKED (/Off)
2025-07-15 20:53:33,893 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_11) to unchecked
2025-07-15 20:53:33,894 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_11)' = False
2025-07-15 20:53:33,900 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.va -> '(Checkbox_11)' = False
2025-07-15 20:53:33,901 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_12)
2025-07-15 20:53:33,901 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:33,901 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_12) existing appearance keys: ['/Checkbox_12', '/Off']
2025-07-15 20:53:33,901 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_12) using field-specific on state: /Checkbox_12
2025-07-15 20:53:33,901 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_12) detected states - On: /Checkbox_12, Off: /Off
2025-07-15 20:53:33,901 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_12, Off: /Off
2025-07-15 20:53:33,902 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_12) to UNCHECKED (/Off)
2025-07-15 20:53:33,902 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_12) to unchecked
2025-07-15 20:53:33,902 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_12)' = False
2025-07-15 20:53:33,907 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.usda -> '(Checkbox_12)' = False
2025-07-15 20:53:33,907 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_13)
2025-07-15 20:53:33,908 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:33,908 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_13) existing appearance keys: ['/Checkbox_13', '/Off']
2025-07-15 20:53:33,908 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_13) using field-specific on state: /Checkbox_13
2025-07-15 20:53:33,908 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_13) detected states - On: /Checkbox_13, Off: /Off
2025-07-15 20:53:33,908 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_13, Off: /Off
2025-07-15 20:53:33,909 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_13) to UNCHECKED (/Off)
2025-07-15 20:53:33,909 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_13) to unchecked
2025-07-15 20:53:33,909 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_13)' = False
2025-07-15 20:53:33,913 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.other -> '(Checkbox_13)' = False
2025-07-15 20:53:33,914 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_1)
2025-07-15 20:53:33,915 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:53:33,915 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_1) existing appearance keys: ['/Checkbox_1', '/Off']
2025-07-15 20:53:33,916 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_1) using field-specific on state: /Checkbox_1
2025-07-15 20:53:33,916 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_1) detected states - On: /Checkbox_1, Off: /Off
2025-07-15 20:53:33,916 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_1, Off: /Off
2025-07-15 20:53:33,916 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_1) to CHECKED (/Checkbox_1)
2025-07-15 20:53:33,917 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_1) to checked
2025-07-15 20:53:33,917 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_1)' = True
2025-07-15 20:53:33,918 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.as_is_condition -> '(Checkbox_1)' = True
2025-07-15 20:53:33,919 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_2)
2025-07-15 20:53:33,919 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:33,921 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_2) existing appearance keys: ['/Checkbox_2', '/Off']
2025-07-15 20:53:33,921 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_2) using field-specific on state: /Checkbox_2
2025-07-15 20:53:33,922 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_2) detected states - On: /Checkbox_2, Off: /Off
2025-07-15 20:53:33,922 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_2, Off: /Off
2025-07-15 20:53:33,922 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_2) to UNCHECKED (/Off)
2025-07-15 20:53:33,922 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_2) to unchecked
2025-07-15 20:53:33,922 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_2)' = False
2025-07-15 20:53:33,924 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.seller_repairs -> '(Checkbox_2)' = False
2025-07-15 20:53:33,924 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_3)
2025-07-15 20:53:33,924 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:53:33,924 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_3) existing appearance keys: ['/Checkbox_3', '/Off']
2025-07-15 20:53:33,925 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_3) using field-specific on state: /Checkbox_3
2025-07-15 20:53:33,925 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_3) detected states - On: /Checkbox_3, Off: /Off
2025-07-15 20:53:33,925 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_3, Off: /Off
2025-07-15 20:53:33,925 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_3) to CHECKED (/Checkbox_3)
2025-07-15 20:53:33,925 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_3) to checked
2025-07-15 20:53:33,926 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_3)' = True
2025-07-15 20:53:33,928 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.buyer_inspection -> '(Checkbox_3)' = True
2025-07-15 20:53:33,929 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_4)
2025-07-15 20:53:33,929 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:53:33,929 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_4) existing appearance keys: ['/Checkbox_4', '/Off']
2025-07-15 20:53:33,930 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_4) using field-specific on state: /Checkbox_4
2025-07-15 20:53:33,930 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_4) detected states - On: /Checkbox_4, Off: /Off
2025-07-15 20:53:33,930 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_4, Off: /Off
2025-07-15 20:53:33,930 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_4) to CHECKED (/Checkbox_4)
2025-07-15 20:53:33,930 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_4) to checked
2025-07-15 20:53:33,930 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_4)' = True
2025-07-15 20:53:33,932 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.professional_inspection -> '(Checkbox_4)' = True
2025-07-15 20:53:33,933 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_5)
2025-07-15 20:53:33,933 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:33,933 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_5) existing appearance keys: ['/Checkbox_5', '/Off']
2025-07-15 20:53:33,933 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_5) using field-specific on state: /Checkbox_5
2025-07-15 20:53:33,933 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_5) detected states - On: /Checkbox_5, Off: /Off
2025-07-15 20:53:33,934 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_5, Off: /Off
2025-07-15 20:53:33,934 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_5) to UNCHECKED (/Off)
2025-07-15 20:53:33,934 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_5) to unchecked
2025-07-15 20:53:33,934 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_5)' = False
2025-07-15 20:53:33,936 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.termite_inspection -> '(Checkbox_5)' = False
2025-07-15 20:53:33,938 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_6)
2025-07-15 20:53:33,938 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:53:33,938 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_6) existing appearance keys: ['/Checkbox_6', '/Off']
2025-07-15 20:53:33,938 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_6) using field-specific on state: /Checkbox_6
2025-07-15 20:53:33,938 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_6) detected states - On: /Checkbox_6, Off: /Off
2025-07-15 20:53:33,939 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_6, Off: /Off
2025-07-15 20:53:33,939 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_6) to CHECKED (/Checkbox_6)
2025-07-15 20:53:33,939 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_6) to checked
2025-07-15 20:53:33,939 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_6)' = True
2025-07-15 20:53:33,940 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.roof_inspection -> '(Checkbox_6)' = True
2025-07-15 20:53:33,941 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_28)
2025-07-15 20:53:33,941 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:53:33,941 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_28) existing appearance keys: ['/Checkbox_28', '/Off']
2025-07-15 20:53:33,941 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_28) using field-specific on state: /Checkbox_28
2025-07-15 20:53:33,941 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_28) detected states - On: /Checkbox_28, Off: /Off
2025-07-15 20:53:33,942 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_28, Off: /Off
2025-07-15 20:53:33,942 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_28) to CHECKED (/Checkbox_28)
2025-07-15 20:53:33,942 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_28) to checked
2025-07-15 20:53:33,942 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_28)' = True
2025-07-15 20:53:33,945 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.refrigerator -> '(Checkbox_28)' = True
2025-07-15 20:53:33,946 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_29)
2025-07-15 20:53:33,946 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:33,946 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_29) existing appearance keys: ['/Checkbox_29', '/Off']
2025-07-15 20:53:33,946 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_29) using field-specific on state: /Checkbox_29
2025-07-15 20:53:33,946 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_29) detected states - On: /Checkbox_29, Off: /Off
2025-07-15 20:53:33,947 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_29, Off: /Off
2025-07-15 20:53:33,947 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_29) to UNCHECKED (/Off)
2025-07-15 20:53:33,947 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_29) to unchecked
2025-07-15 20:53:33,947 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_29)' = False
2025-07-15 20:53:33,948 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.washer -> '(Checkbox_29)' = False
2025-07-15 20:53:33,949 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_30)
2025-07-15 20:53:33,949 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:53:33,949 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_30) existing appearance keys: ['/Checkbox_30', '/Off']
2025-07-15 20:53:33,949 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_30) using field-specific on state: /Checkbox_30
2025-07-15 20:53:33,949 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_30) detected states - On: /Checkbox_30, Off: /Off
2025-07-15 20:53:33,949 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_30, Off: /Off
2025-07-15 20:53:33,950 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_30) to CHECKED (/Checkbox_30)
2025-07-15 20:53:33,950 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_30) to checked
2025-07-15 20:53:33,950 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_30)' = True
2025-07-15 20:53:33,951 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.dryer -> '(Checkbox_30)' = True
2025-07-15 20:53:33,955 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_31)
2025-07-15 20:53:33,955 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:33,956 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_31) existing appearance keys: ['/Checkbox_31', '/Off']
2025-07-15 20:53:33,956 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_31) using field-specific on state: /Checkbox_31
2025-07-15 20:53:33,956 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_31) detected states - On: /Checkbox_31, Off: /Off
2025-07-15 20:53:33,956 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_31, Off: /Off
2025-07-15 20:53:33,956 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_31) to UNCHECKED (/Off)
2025-07-15 20:53:33,957 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_31) to unchecked
2025-07-15 20:53:33,957 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_31)' = False
2025-07-15 20:53:33,958 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.dishwasher -> '(Checkbox_31)' = False
2025-07-15 20:53:33,959 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_32)
2025-07-15 20:53:33,959 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 20:53:33,959 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_32) existing appearance keys: ['/Checkbox_32', '/Off']
2025-07-15 20:53:33,959 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_32) using field-specific on state: /Checkbox_32
2025-07-15 20:53:33,959 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_32) detected states - On: /Checkbox_32, Off: /Off
2025-07-15 20:53:33,959 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_32, Off: /Off
2025-07-15 20:53:33,959 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_32) to UNCHECKED (/Off)
2025-07-15 20:53:33,960 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_32) to unchecked
2025-07-15 20:53:33,960 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_32)' = False
2025-07-15 20:53:33,981 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.microwave -> '(Checkbox_32)' = False
2025-07-15 20:53:33,982 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_33)
2025-07-15 20:53:33,983 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 20:53:33,983 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_33) existing appearance keys: ['/Checkbox_33', '/Off']
2025-07-15 20:53:33,983 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_33) using field-specific on state: /Checkbox_33
2025-07-15 20:53:33,983 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_33) detected states - On: /Checkbox_33, Off: /Off
2025-07-15 20:53:33,983 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_33, Off: /Off
2025-07-15 20:53:33,984 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_33) to CHECKED (/Checkbox_33)
2025-07-15 20:53:33,987 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_33) to checked
2025-07-15 20:53:33,988 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_33)' = True
2025-07-15 20:53:33,989 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.oven_range -> '(Checkbox_33)' = True
2025-07-15 20:53:34,101 - __main__ - INFO - fill_pdf:1103 - PDF generated successfully. Processed: 21, Failed: 0
2025-07-15 20:53:34,101 - __main__ - INFO - fill_pdf:1266 - PDF generated successfully. Fields processed: 21, Failed: 0
2025-07-15 20:53:34,102 - __main__ - INFO - fill_pdf:1302 - Checkbox debug mode disabled
2025-07-15 20:53:34,104 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 20:53:34] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 21:31:29,419 - __main__ - INFO - fill_pdf:1248 - Received PDF fill request with 49 fields
2025-07-15 21:31:29,425 - __main__ - INFO - fill_pdf:1253 - Checkbox debug mode enabled
2025-07-15 21:31:29,565 - __main__ - INFO - fill_pdf:1066 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 21:31:29,566 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_1)' = John Michael Smith
2025-07-15 21:31:29,569 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: seller_name -> '(Text_1)' = John Michael Smith
2025-07-15 21:31:29,570 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_2)' = Jane Elizabeth Doe
2025-07-15 21:31:29,575 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: buyer_name -> '(Text_2)' = Jane Elizabeth Doe
2025-07-15 21:31:29,576 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Number_1)' = 875000
2025-07-15 21:31:29,583 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: purchase_price -> '(Number_1)' = 875000
2025-07-15 21:31:29,583 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: earnest_money
2025-07-15 21:31:29,583 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_3)' = 1234 Maple Street, Beverly Hills, CA 90210
2025-07-15 21:31:29,588 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: property_address -> '(Text_3)' = 1234 Maple Street, Beverly Hills, CA 90210
2025-07-15 21:31:29,589 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_6)' = Lot 15, Block 3, Sunset Estates Subdivision, as recorded in Plat Book 45, Page 67
2025-07-15 21:31:29,592 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: legal_description -> '(Text_6)' = Lot 15, Block 3, Sunset Estates Subdivision, as recorded in Plat Book 45, Page 67
2025-07-15 21:31:29,596 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Date_2)' = 2025-08-15
2025-07-15 21:31:29,597 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: closing_date -> '(Date_2)' = 2025-08-15
2025-07-15 21:31:29,600 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_52)' = 2025-08-20
2025-07-15 21:31:29,600 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: possession_date -> '(Text_52)' = 2025-08-20
2025-07-15 21:31:29,603 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_14)' = Premier Title & Escrow Services
2025-07-15 21:31:29,607 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: title_company -> '(Text_14)' = Premier Title & Escrow Services
2025-07-15 21:31:29,607 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: title_policy_amount
2025-07-15 21:31:29,607 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: survey_required
2025-07-15 21:31:29,608 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: deed_restrictions
2025-07-15 21:31:29,614 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_36)' = 350
2025-07-15 21:31:29,616 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: hoa_fees -> '(Text_36)' = 350
2025-07-15 21:31:29,624 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_37)' = 12500
2025-07-15 21:31:29,625 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: property_taxes -> '(Text_37)' = 12500
2025-07-15 21:31:29,634 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_35)' = 0
2025-07-15 21:31:29,639 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: special_assessments -> '(Text_35)' = 0
2025-07-15 21:31:29,640 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: utilities_included
2025-07-15 21:31:29,640 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: mineral_rights
2025-07-15 21:31:29,641 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_8)
2025-07-15 21:31:29,642 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 21:31:29,646 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_8) existing appearance keys: ['/Checkbox_8', '/Off']
2025-07-15 21:31:29,647 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_8) using field-specific on state: /Checkbox_8
2025-07-15 21:31:29,647 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_8) detected states - On: /Checkbox_8, Off: /Off
2025-07-15 21:31:29,648 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_8, Off: /Off
2025-07-15 21:31:29,648 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_8) to UNCHECKED (/Off)
2025-07-15 21:31:29,654 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_8) to unchecked
2025-07-15 21:31:29,656 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_8)' = False
2025-07-15 21:31:29,663 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.cash -> '(Checkbox_8)' = False
2025-07-15 21:31:29,665 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_9)
2025-07-15 21:31:29,666 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 21:31:29,671 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_9) existing appearance keys: ['/Checkbox_9', '/Off']
2025-07-15 21:31:29,671 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_9) using field-specific on state: /Checkbox_9
2025-07-15 21:31:29,671 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_9) detected states - On: /Checkbox_9, Off: /Off
2025-07-15 21:31:29,671 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_9, Off: /Off
2025-07-15 21:31:29,672 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_9) to CHECKED (/Checkbox_9)
2025-07-15 21:31:29,672 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_9) to checked
2025-07-15 21:31:29,672 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_9)' = True
2025-07-15 21:31:29,673 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.conventional -> '(Checkbox_9)' = True
2025-07-15 21:31:29,674 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_10)
2025-07-15 21:31:29,674 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 21:31:29,674 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_10) existing appearance keys: ['/Checkbox_10', '/Off']
2025-07-15 21:31:29,674 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_10) using field-specific on state: /Checkbox_10
2025-07-15 21:31:29,674 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_10) detected states - On: /Checkbox_10, Off: /Off
2025-07-15 21:31:29,675 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_10, Off: /Off
2025-07-15 21:31:29,677 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_10) to UNCHECKED (/Off)
2025-07-15 21:31:29,677 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_10) to unchecked
2025-07-15 21:31:29,677 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_10)' = False
2025-07-15 21:31:29,679 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.fha -> '(Checkbox_10)' = False
2025-07-15 21:31:29,679 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_11)
2025-07-15 21:31:29,679 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 21:31:29,679 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_11) existing appearance keys: ['/Checkbox_11', '/Off']
2025-07-15 21:31:29,680 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_11) using field-specific on state: /Checkbox_11
2025-07-15 21:31:29,680 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_11) detected states - On: /Checkbox_11, Off: /Off
2025-07-15 21:31:29,680 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_11, Off: /Off
2025-07-15 21:31:29,680 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_11) to UNCHECKED (/Off)
2025-07-15 21:31:29,680 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_11) to unchecked
2025-07-15 21:31:29,680 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_11)' = False
2025-07-15 21:31:29,682 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.va -> '(Checkbox_11)' = False
2025-07-15 21:31:29,687 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_12)
2025-07-15 21:31:29,687 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 21:31:29,688 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_12) existing appearance keys: ['/Checkbox_12', '/Off']
2025-07-15 21:31:29,688 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_12) using field-specific on state: /Checkbox_12
2025-07-15 21:31:29,689 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_12) detected states - On: /Checkbox_12, Off: /Off
2025-07-15 21:31:29,689 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_12, Off: /Off
2025-07-15 21:31:29,689 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_12) to UNCHECKED (/Off)
2025-07-15 21:31:29,689 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_12) to unchecked
2025-07-15 21:31:29,690 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_12)' = False
2025-07-15 21:31:29,695 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.usda -> '(Checkbox_12)' = False
2025-07-15 21:31:29,696 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_13)
2025-07-15 21:31:29,697 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 21:31:29,702 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_13) existing appearance keys: ['/Checkbox_13', '/Off']
2025-07-15 21:31:29,703 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_13) using field-specific on state: /Checkbox_13
2025-07-15 21:31:29,703 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_13) detected states - On: /Checkbox_13, Off: /Off
2025-07-15 21:31:29,703 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_13, Off: /Off
2025-07-15 21:31:29,703 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_13) to UNCHECKED (/Off)
2025-07-15 21:31:29,704 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_13) to unchecked
2025-07-15 21:31:29,704 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_13)' = False
2025-07-15 21:31:29,706 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: financing.other -> '(Checkbox_13)' = False
2025-07-15 21:31:29,707 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Number_4)' = 700000
2025-07-15 21:31:29,710 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: loan_amount -> '(Number_4)' = 700000
2025-07-15 21:31:29,710 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Number_5)' = 175000
2025-07-15 21:31:29,712 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: down_payment -> '(Number_5)' = 175000
2025-07-15 21:31:29,712 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: interest_rate
2025-07-15 21:31:29,712 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: loan_term
2025-07-15 21:31:29,712 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_17)' = First National Bank of California
2025-07-15 21:31:29,714 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: lender_name -> '(Text_17)' = First National Bank of California
2025-07-15 21:31:29,714 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: loan_contingency_date
2025-07-15 21:31:29,714 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: appraisal_contingency
2025-07-15 21:31:29,714 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: appraisal_amount
2025-07-15 21:31:29,715 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_1)
2025-07-15 21:31:29,717 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 21:31:29,717 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_1) existing appearance keys: ['/Checkbox_1', '/Off']
2025-07-15 21:31:29,718 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_1) using field-specific on state: /Checkbox_1
2025-07-15 21:31:29,718 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_1) detected states - On: /Checkbox_1, Off: /Off
2025-07-15 21:31:29,718 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_1, Off: /Off
2025-07-15 21:31:29,718 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_1) to UNCHECKED (/Off)
2025-07-15 21:31:29,718 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_1) to unchecked
2025-07-15 21:31:29,718 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_1)' = False
2025-07-15 21:31:29,720 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.as_is_condition -> '(Checkbox_1)' = False
2025-07-15 21:31:29,720 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_2)
2025-07-15 21:31:29,720 - __main__ - DEBUG - _handle_button_field:146 - Converted value False to boolean: False
2025-07-15 21:31:29,721 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_2) existing appearance keys: ['/Checkbox_2', '/Off']
2025-07-15 21:31:29,722 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_2) using field-specific on state: /Checkbox_2
2025-07-15 21:31:29,722 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_2) detected states - On: /Checkbox_2, Off: /Off
2025-07-15 21:31:29,723 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_2, Off: /Off
2025-07-15 21:31:29,723 - __main__ - DEBUG - _handle_button_field:169 - Set checkbox (Checkbox_2) to UNCHECKED (/Off)
2025-07-15 21:31:29,723 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_2) to unchecked
2025-07-15 21:31:29,723 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_2)' = False
2025-07-15 21:31:29,726 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.seller_repairs -> '(Checkbox_2)' = False
2025-07-15 21:31:29,726 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_3)
2025-07-15 21:31:29,727 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 21:31:29,727 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_3) existing appearance keys: ['/Checkbox_3', '/Off']
2025-07-15 21:31:29,727 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_3) using field-specific on state: /Checkbox_3
2025-07-15 21:31:29,728 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_3) detected states - On: /Checkbox_3, Off: /Off
2025-07-15 21:31:29,728 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_3, Off: /Off
2025-07-15 21:31:29,728 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_3) to CHECKED (/Checkbox_3)
2025-07-15 21:31:29,728 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_3) to checked
2025-07-15 21:31:29,728 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_3)' = True
2025-07-15 21:31:29,730 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.buyer_inspection -> '(Checkbox_3)' = True
2025-07-15 21:31:29,730 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_4)
2025-07-15 21:31:29,730 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 21:31:29,730 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_4) existing appearance keys: ['/Checkbox_4', '/Off']
2025-07-15 21:31:29,730 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_4) using field-specific on state: /Checkbox_4
2025-07-15 21:31:29,731 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_4) detected states - On: /Checkbox_4, Off: /Off
2025-07-15 21:31:29,731 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_4, Off: /Off
2025-07-15 21:31:29,731 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_4) to CHECKED (/Checkbox_4)
2025-07-15 21:31:29,732 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_4) to checked
2025-07-15 21:31:29,734 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_4)' = True
2025-07-15 21:31:29,736 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.professional_inspection -> '(Checkbox_4)' = True
2025-07-15 21:31:29,736 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_5)
2025-07-15 21:31:29,736 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 21:31:29,736 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_5) existing appearance keys: ['/Checkbox_5', '/Off']
2025-07-15 21:31:29,736 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_5) using field-specific on state: /Checkbox_5
2025-07-15 21:31:29,736 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_5) detected states - On: /Checkbox_5, Off: /Off
2025-07-15 21:31:29,737 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_5, Off: /Off
2025-07-15 21:31:29,737 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_5) to CHECKED (/Checkbox_5)
2025-07-15 21:31:29,737 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_5) to checked
2025-07-15 21:31:29,737 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_5)' = True
2025-07-15 21:31:29,739 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.termite_inspection -> '(Checkbox_5)' = True
2025-07-15 21:31:29,742 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_6)
2025-07-15 21:31:29,742 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 21:31:29,743 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_6) existing appearance keys: ['/Checkbox_6', '/Off']
2025-07-15 21:31:29,743 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_6) using field-specific on state: /Checkbox_6
2025-07-15 21:31:29,743 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_6) detected states - On: /Checkbox_6, Off: /Off
2025-07-15 21:31:29,743 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_6, Off: /Off
2025-07-15 21:31:29,743 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_6) to CHECKED (/Checkbox_6)
2025-07-15 21:31:29,743 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_6) to checked
2025-07-15 21:31:29,744 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_6)' = True
2025-07-15 21:31:29,745 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: inspections.roof_inspection -> '(Checkbox_6)' = True
2025-07-15 21:31:29,745 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: inspection_period
2025-07-15 21:31:29,745 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: repair_limit
2025-07-15 21:31:29,745 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: inspection_contingency_date
2025-07-15 21:31:29,746 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_28)
2025-07-15 21:31:29,746 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 21:31:29,747 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_28) existing appearance keys: ['/Checkbox_28', '/Off']
2025-07-15 21:31:29,747 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_28) using field-specific on state: /Checkbox_28
2025-07-15 21:31:29,747 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_28) detected states - On: /Checkbox_28, Off: /Off
2025-07-15 21:31:29,750 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_28, Off: /Off
2025-07-15 21:31:29,750 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_28) to CHECKED (/Checkbox_28)
2025-07-15 21:31:29,751 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_28) to checked
2025-07-15 21:31:29,751 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_28)' = True
2025-07-15 21:31:29,752 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.refrigerator -> '(Checkbox_28)' = True
2025-07-15 21:31:29,755 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_29)
2025-07-15 21:31:29,755 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 21:31:29,758 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_29) existing appearance keys: ['/Checkbox_29', '/Off']
2025-07-15 21:31:29,759 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_29) using field-specific on state: /Checkbox_29
2025-07-15 21:31:29,759 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_29) detected states - On: /Checkbox_29, Off: /Off
2025-07-15 21:31:29,759 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_29, Off: /Off
2025-07-15 21:31:29,759 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_29) to CHECKED (/Checkbox_29)
2025-07-15 21:31:29,760 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_29) to checked
2025-07-15 21:31:29,760 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_29)' = True
2025-07-15 21:31:29,762 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.washer -> '(Checkbox_29)' = True
2025-07-15 21:31:29,763 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_30)
2025-07-15 21:31:29,763 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 21:31:29,764 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_30) existing appearance keys: ['/Checkbox_30', '/Off']
2025-07-15 21:31:29,764 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_30) using field-specific on state: /Checkbox_30
2025-07-15 21:31:29,764 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_30) detected states - On: /Checkbox_30, Off: /Off
2025-07-15 21:31:29,764 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_30, Off: /Off
2025-07-15 21:31:29,764 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_30) to CHECKED (/Checkbox_30)
2025-07-15 21:31:29,764 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_30) to checked
2025-07-15 21:31:29,764 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_30)' = True
2025-07-15 21:31:29,766 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.dryer -> '(Checkbox_30)' = True
2025-07-15 21:31:29,767 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_31)
2025-07-15 21:31:29,767 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 21:31:29,767 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_31) existing appearance keys: ['/Checkbox_31', '/Off']
2025-07-15 21:31:29,767 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_31) using field-specific on state: /Checkbox_31
2025-07-15 21:31:29,767 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_31) detected states - On: /Checkbox_31, Off: /Off
2025-07-15 21:31:29,767 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_31, Off: /Off
2025-07-15 21:31:29,767 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_31) to CHECKED (/Checkbox_31)
2025-07-15 21:31:29,767 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_31) to checked
2025-07-15 21:31:29,767 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_31)' = True
2025-07-15 21:31:29,769 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.dishwasher -> '(Checkbox_31)' = True
2025-07-15 21:31:29,770 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_32)
2025-07-15 21:31:29,770 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 21:31:29,770 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_32) existing appearance keys: ['/Checkbox_32', '/Off']
2025-07-15 21:31:29,770 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_32) using field-specific on state: /Checkbox_32
2025-07-15 21:31:29,770 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_32) detected states - On: /Checkbox_32, Off: /Off
2025-07-15 21:31:29,770 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_32, Off: /Off
2025-07-15 21:31:29,770 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_32) to CHECKED (/Checkbox_32)
2025-07-15 21:31:29,771 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_32) to checked
2025-07-15 21:31:29,771 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_32)' = True
2025-07-15 21:31:29,772 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.microwave -> '(Checkbox_32)' = True
2025-07-15 21:31:29,774 - __main__ - DEBUG - _handle_button_field:142 - Processing checkbox field: (Checkbox_33)
2025-07-15 21:31:29,774 - __main__ - DEBUG - _handle_button_field:146 - Converted value True to boolean: True
2025-07-15 21:31:29,775 - __main__ - DEBUG - _analyze_existing_appearance_states:204 - Field (Checkbox_33) existing appearance keys: ['/Checkbox_33', '/Off']
2025-07-15 21:31:29,775 - __main__ - DEBUG - _analyze_existing_appearance_states:221 - Field (Checkbox_33) using field-specific on state: /Checkbox_33
2025-07-15 21:31:29,775 - __main__ - DEBUG - _analyze_existing_appearance_states:229 - Field (Checkbox_33) detected states - On: /Checkbox_33, Off: /Off
2025-07-15 21:31:29,775 - __main__ - DEBUG - _handle_button_field:150 - Detected appearance states - On: /Checkbox_33, Off: /Off
2025-07-15 21:31:29,775 - __main__ - DEBUG - _handle_button_field:158 - Set checkbox (Checkbox_33) to CHECKED (/Checkbox_33)
2025-07-15 21:31:29,775 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_33) to checked
2025-07-15 21:31:29,775 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Checkbox_33)' = True
2025-07-15 21:31:29,776 - __main__ - DEBUG - _fill_checkbox_group:1163 - Successfully filled checkbox: appliances.oven_range -> '(Checkbox_33)' = True
2025-07-15 21:31:29,777 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_10)' = All light fixtures, ceiling fans, built-in shelving, window treatments
2025-07-15 21:31:29,778 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: fixtures_included -> '(Text_10)' = All light fixtures, ceiling fans, built-in shelving, window treatments
2025-07-15 21:31:29,778 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: personal_property
2025-07-15 21:31:29,779 - __main__ - WARNING - _fill_checkbox_group:1148 - No checkbox group mapping found for: warranties
2025-07-15 21:31:29,779 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: warranty_company
2025-07-15 21:31:29,779 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: warranty_cost
2025-07-15 21:31:29,779 - __main__ - WARNING - _fill_checkbox_group:1148 - No checkbox group mapping found for: seller_disclosures
2025-07-15 21:31:29,782 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_49)' = Seller to provide 1-year home warranty. Pool equipment and maintenance included for 6 months.
2025-07-15 21:31:29,782 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: additional_terms -> '(Text_49)' = Seller to provide 1-year home warranty. Pool equipment and maintenance included for 6 months.
2025-07-15 21:31:29,784 - __main__ - DEBUG - set_pdf_field:632 - Successfully set field '(Text_50)' = Sale contingent upon buyer selling current residence by 2025-07-30
2025-07-15 21:31:29,784 - __main__ - DEBUG - _fill_regular_field:1132 - Successfully filled field: contingencies -> '(Text_50)' = Sale contingent upon buyer selling current residence by 2025-07-30
2025-07-15 21:31:29,784 - __main__ - WARNING - _fill_checkbox_group:1148 - No checkbox group mapping found for: closing_costs
2025-07-15 21:31:29,785 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: prorations
2025-07-15 21:31:29,785 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: default_provisions
2025-07-15 21:31:29,785 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: dispute_resolution
2025-07-15 21:31:29,785 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: governing_law
2025-07-15 21:31:29,785 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: additional_provisions
2025-07-15 21:31:29,785 - __main__ - WARNING - _fill_checkbox_group:1148 - No checkbox group mapping found for: seller_agent
2025-07-15 21:31:29,786 - __main__ - WARNING - _fill_checkbox_group:1148 - No checkbox group mapping found for: buyer_agent
2025-07-15 21:31:29,786 - __main__ - WARNING - _fill_checkbox_group:1148 - No checkbox group mapping found for: signatures
2025-07-15 21:31:29,786 - __main__ - WARNING - _fill_checkbox_group:1148 - No checkbox group mapping found for: notary
2025-07-15 21:31:29,854 - __main__ - INFO - fill_pdf:1103 - PDF generated successfully. Processed: 35, Failed: 48
2025-07-15 21:31:29,854 - __main__ - INFO - fill_pdf:1266 - PDF generated successfully. Fields processed: 35, Failed: 48
2025-07-15 21:31:29,856 - __main__ - INFO - fill_pdf:1302 - Checkbox debug mode disabled
2025-07-15 21:31:29,857 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 21:31:29] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 21:34:31,196 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 21:34:31] "GET /extract-fields HTTP/1.1" 200 -
2025-07-15 21:34:31,253 - __main__ - INFO - save_field_mapping:760 - Field mapping saved successfully
2025-07-15 21:34:31,257 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 21:34:31] "POST /update-mapping HTTP/1.1" 200 -
2025-07-15 21:34:31,284 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 21:34:31] "POST /validate-form HTTP/1.1" 200 -
2025-07-15 21:34:31,346 - __main__ - INFO - fill_pdf:1248 - Received PDF fill request with 129 fields
2025-07-15 21:34:31,668 - __main__ - INFO - fill_pdf:1066 - Loaded PDF template: as_is_contract_fillable.pdf
2025-07-15 21:34:31,804 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_8) to unchecked
2025-07-15 21:34:31,812 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_9) to checked
2025-07-15 21:34:31,819 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_10) to unchecked
2025-07-15 21:34:31,834 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_11) to unchecked
2025-07-15 21:34:31,840 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_12) to unchecked
2025-07-15 21:34:31,845 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_13) to unchecked
2025-07-15 21:34:31,853 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_1) to checked
2025-07-15 21:34:31,859 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_2) to unchecked
2025-07-15 21:34:31,865 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_3) to checked
2025-07-15 21:34:31,871 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_4) to checked
2025-07-15 21:34:31,876 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_5) to checked
2025-07-15 21:34:31,882 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_6) to checked
2025-07-15 21:34:31,886 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_7) to checked
2025-07-15 21:34:31,892 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_14) to checked
2025-07-15 21:34:31,896 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_15) to checked
2025-07-15 21:34:31,903 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_16) to checked
2025-07-15 21:34:31,907 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_17) to checked
2025-07-15 21:34:31,912 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_18) to unchecked
2025-07-15 21:34:31,917 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_19) to unchecked
2025-07-15 21:34:31,922 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_20) to unchecked
2025-07-15 21:34:31,926 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_21) to checked
2025-07-15 21:34:31,932 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_22) to checked
2025-07-15 21:34:31,937 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_23) to checked
2025-07-15 21:34:31,943 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_24) to unchecked
2025-07-15 21:34:31,947 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_25) to unchecked
2025-07-15 21:34:31,953 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_26) to checked
2025-07-15 21:34:31,958 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_27) to checked
2025-07-15 21:34:31,965 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_28) to checked
2025-07-15 21:34:31,970 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_29) to checked
2025-07-15 21:34:31,976 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_30) to checked
2025-07-15 21:34:31,982 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_31) to checked
2025-07-15 21:34:31,988 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_32) to checked
2025-07-15 21:34:31,995 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_33) to checked
2025-07-15 21:34:32,003 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_34) to checked
2025-07-15 21:34:32,012 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_35) to checked
2025-07-15 21:34:32,017 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_36) to checked
2025-07-15 21:34:32,024 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_37) to checked
2025-07-15 21:34:32,030 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_38) to checked
2025-07-15 21:34:32,037 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_39) to checked
2025-07-15 21:34:32,044 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_40) to checked
2025-07-15 21:34:32,052 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_41) to checked
2025-07-15 21:34:32,058 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_42) to checked
2025-07-15 21:34:32,065 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_43) to checked
2025-07-15 21:34:32,071 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_44) to checked
2025-07-15 21:34:32,077 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_45) to checked
2025-07-15 21:34:32,082 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_46) to checked
2025-07-15 21:34:32,087 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_47) to checked
2025-07-15 21:34:32,093 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_48) to checked
2025-07-15 21:34:32,099 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_49) to checked
2025-07-15 21:34:32,104 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_50) to checked
2025-07-15 21:34:32,110 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_51) to checked
2025-07-15 21:34:32,115 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_52) to checked
2025-07-15 21:34:32,120 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_53) to checked
2025-07-15 21:34:32,126 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_54) to checked
2025-07-15 21:34:32,131 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_55) to checked
2025-07-15 21:34:32,135 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_56) to unchecked
2025-07-15 21:34:32,142 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_72) to checked
2025-07-15 21:34:32,146 - __main__ - INFO - _handle_button_field:176 - Successfully set checkbox (Checkbox_73) to checked
2025-07-15 21:34:32,501 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: signature_date_buyer_1
2025-07-15 21:34:32,501 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: signature_date_buyer_2
2025-07-15 21:34:32,502 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: signature_date_seller_1
2025-07-15 21:34:32,502 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: signature_date_seller_2
2025-07-15 21:34:32,502 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: notary_signature
2025-07-15 21:34:32,502 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: notary_date
2025-07-15 21:34:32,503 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: witness_1
2025-07-15 21:34:32,503 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: witness_2
2025-07-15 21:34:32,503 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: witness_date_1
2025-07-15 21:34:32,503 - __main__ - WARNING - _fill_regular_field:1125 - No PDF field mapping found for: witness_date_2
2025-07-15 21:34:32,672 - __main__ - INFO - fill_pdf:1103 - PDF generated successfully. Processed: 173, Failed: 10
2025-07-15 21:34:32,672 - __main__ - INFO - fill_pdf:1266 - PDF generated successfully. Fields processed: 173, Failed: 10
2025-07-15 21:34:32,673 - werkzeug - INFO - _log:187 - 127.0.0.1 - - [15/Jul/2025 21:34:32] "POST /fill-pdf HTTP/1.1" 200 -
2025-07-15 23:06:15,553 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-15 23:06:16,020 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 23:06:17,729 - __main__ - INFO - _load_pdf_fields_info:87 - Loaded 188 PDF field definitions
2025-07-15 23:06:17,731 - __main__ - INFO - _load_field_mapping:715 - Loaded field mapping with 115 fields
2025-07-15 23:06:17,731 - __main__ - WARNING - _load_field_mapping_guide:733 - Field mapping guide not found
2025-07-15 23:06:17,732 - __main__ - INFO - _load_pdf_fields_info:745 - Loaded 188 PDF field definitions
2025-07-15 23:06:17,790 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 23:06:17,823 - werkzeug - INFO - _log:187 -  * Debugger PIN: 405-311-689
2025-07-15 23:07:38,160 - werkzeug - INFO - _log:187 -  * Detected change in '/Volumes/TeamGroup 512/Desktop/Mapping/app.py', reloading
2025-07-15 23:07:38,255 - werkzeug - INFO - _log:187 -  * Restarting with stat
2025-07-15 23:07:39,342 - __main__ - INFO - _load_pdf_fields_info:100 - Loaded 188 PDF field definitions
2025-07-15 23:07:39,343 - __main__ - INFO - _load_field_mapping:728 - Loaded field mapping with 115 fields
2025-07-15 23:07:39,344 - __main__ - WARNING - _load_field_mapping_guide:746 - Field mapping guide not found
2025-07-15 23:07:39,345 - __main__ - INFO - _load_pdf_fields_info:758 - Loaded 188 PDF field definitions
2025-07-15 23:07:39,425 - werkzeug - WARNING - _log:187 -  * Debugger is active!
2025-07-15 23:07:39,453 - werkzeug - INFO - _log:187 -  * Debugger PIN: 405-311-689
