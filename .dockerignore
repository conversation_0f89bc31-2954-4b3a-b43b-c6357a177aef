# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Test files
*test*.pdf
test_*.py
pytest_cache/

# Development files
.env
.env.local

# Git
.git/
.gitignore

# Documentation
README.md
docs/

# Temporary files
tmp/
temp/
