#!/usr/bin/env python3
"""
Comprehensive test for enhanced checkbox functionality
"""

import requests
import json
import logging
from datetime import datetime

# Configuration
BACKEND_URL = "http://localhost:5000"

# Enable debug logging for checkbox testing
logging.basicConfig(level=logging.DEBUG)

def test_checkbox_functionality():
    """Test the enhanced checkbox handling with various input types and edge cases"""
    
    print("🔲 ENHANCED CHECKBOX FUNCTIONALITY TEST")
    print("=" * 50)
    
    # Test data with various checkbox input formats
    test_cases = [
        {
            "name": "Boolean Values Test",
            "data": {
                "financing": {
                    "cash": True,
                    "conventional": False,
                    "fha": True,
                    "va": False
                },
                "inspections": {
                    "as_is_condition": True,
                    "buyer_inspection": True,
                    "professional_inspection": False
                }
            }
        },
        {
            "name": "String Values Test", 
            "data": {
                "financing": {
                    "cash": "true",
                    "conventional": "false",
                    "fha": "yes",
                    "va": "no"
                },
                "inspections": {
                    "as_is_condition": "on",
                    "buyer_inspection": "checked",
                    "professional_inspection": "off"
                }
            }
        },
        {
            "name": "Numeric Values Test",
            "data": {
                "financing": {
                    "cash": 1,
                    "conventional": 0,
                    "fha": 1,
                    "va": 0
                },
                "inspections": {
                    "as_is_condition": 1,
                    "buyer_inspection": 1,
                    "professional_inspection": 0
                }
            }
        },
        {
            "name": "Mixed Values Test",
            "data": {
                "financing": {
                    "cash": True,
                    "conventional": "false",
                    "fha": 1,
                    "va": "no"
                },
                "inspections": {
                    "as_is_condition": "yes",
                    "buyer_inspection": True,
                    "professional_inspection": 0
                },
                "appliances": {
                    "refrigerator": "checked",
                    "washer": True,
                    "dryer": 1,
                    "dishwasher": "on",
                    "microwave": False,
                    "oven_range": "off"
                }
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ {test_case['name']}")
        print("-" * 30)
        
        # Add some basic text fields to make it a complete form
        form_data = {
            "seller_name": f"Test Seller {i}",
            "buyer_name": f"Test Buyer {i}",
            "purchase_price": 500000 + (i * 10000),
            **test_case['data']
        }
        
        # Test validation first
        print("   📋 Validating form data...")
        try:
            response = requests.post(
                f"{BACKEND_URL}/validate-form",
                json=form_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                validation_result = response.json()
                print(f"   ✅ Validation: {validation_result['status']}")
                print(f"   📊 Processed fields: {validation_result['processed_fields']}")
                
                if validation_result.get('warnings'):
                    print(f"   ⚠️ Warnings: {len(validation_result['warnings'])}")
                
                if validation_result.get('errors'):
                    print(f"   ❌ Errors: {len(validation_result['errors'])}")
                    for error in validation_result['errors'][:2]:
                        print(f"      - {error}")
            else:
                print(f"   ❌ Validation failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Validation error: {e}")
        
        # Test PDF generation
        print("   📄 Generating PDF...")
        try:
            response = requests.post(
                f"{BACKEND_URL}/fill-pdf",
                json=form_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                # Save the PDF
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"checkbox_test_{i}_{timestamp}.pdf"
                
                with open(filename, 'wb') as f:
                    f.write(response.content)
                
                print(f"   🎉 PDF generated: {filename}")
                print(f"   📄 Size: {len(response.content):,} bytes")
                
                # Analyze checkbox data in the request
                checkbox_count = 0
                for key, value in form_data.items():
                    if isinstance(value, dict):
                        checkbox_count += len(value)
                        print(f"   ☑️ {key}: {sum(1 for v in value.values() if _convert_test_value(v))} checked out of {len(value)}")
                
                print(f"   📊 Total checkboxes: {checkbox_count}")
                
            else:
                print(f"   ❌ PDF generation failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"      Status: {error_data.get('status', 'unknown')}")
                    print(f"      Fields processed: {error_data.get('fields_processed', 0)}")
                    print(f"      Fields failed: {error_data.get('fields_failed', 0)}")
                    
                    if error_data.get('errors'):
                        print("      Errors:")
                        for error in error_data['errors'][:2]:
                            print(f"        - {error}")
                            
                except json.JSONDecodeError:
                    print(f"      Raw response: {response.text[:200]}...")
                    
        except Exception as e:
            print(f"   ❌ PDF generation error: {e}")


def _convert_test_value(value):
    """Helper function to convert test values to boolean for counting"""
    if isinstance(value, bool):
        return value
    elif isinstance(value, str):
        return value.lower() in ('true', '1', 'yes', 'on', 'checked')
    elif isinstance(value, (int, float)):
        return bool(value) and value != 0
    else:
        return bool(value)


def test_edge_cases():
    """Test edge cases and error conditions for checkbox handling"""
    
    print("\n🔍 CHECKBOX EDGE CASES TEST")
    print("=" * 40)
    
    edge_cases = [
        {
            "name": "Empty Values",
            "data": {
                "financing": {
                    "cash": "",
                    "conventional": None,
                    "fha": [],
                    "va": {}
                }
            }
        },
        {
            "name": "Unusual String Values",
            "data": {
                "financing": {
                    "cash": "TRUE",
                    "conventional": "False",
                    "fha": "Y",
                    "va": "N"
                }
            }
        },
        {
            "name": "Numeric Edge Cases",
            "data": {
                "financing": {
                    "cash": -1,
                    "conventional": 0.0,
                    "fha": 2,
                    "va": 0.5
                }
            }
        }
    ]
    
    for i, test_case in enumerate(edge_cases, 1):
        print(f"\n{i}️⃣ {test_case['name']}")
        print("-" * 25)
        
        form_data = {
            "seller_name": f"Edge Test {i}",
            "buyer_name": f"Edge Buyer {i}",
            **test_case['data']
        }
        
        try:
            response = requests.post(
                f"{BACKEND_URL}/validate-form",
                json=form_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Validation handled edge case successfully")
                print(f"   📊 Processed: {result['processed_fields']} fields")
                
                if result.get('warnings'):
                    print(f"   ⚠️ Warnings: {len(result['warnings'])}")
                
                if result.get('errors'):
                    print(f"   ❌ Errors: {len(result['errors'])}")
            else:
                print(f"   ⚠️ Validation response: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Edge case error: {e}")


def test_checkbox_debugging():
    """Test the debugging capabilities of the enhanced checkbox system"""
    
    print("\n🐛 CHECKBOX DEBUGGING TEST")
    print("=" * 35)
    
    # Create a form with various checkbox scenarios
    debug_form_data = {
        "seller_name": "Debug Test Seller",
        "buyer_name": "Debug Test Buyer",
        "purchase_price": 750000,
        
        # Test all checkbox groups
        "financing": {
            "cash": True,
            "conventional": False,
            "fha": "yes",
            "va": 0,
            "usda": "checked",
            "other": 1
        },
        
        "inspections": {
            "as_is_condition": True,
            "seller_repairs": False,
            "buyer_inspection": "on",
            "professional_inspection": "true",
            "termite_inspection": 1,
            "roof_inspection": "yes"
        },
        
        "appliances": {
            "refrigerator": True,
            "washer": "checked",
            "dryer": 1,
            "dishwasher": "on",
            "microwave": False,
            "oven_range": "off"
        }
    }
    
    print("📋 Testing comprehensive checkbox debugging...")
    
    try:
        # Enable debug mode by setting a special header
        response = requests.post(
            f"{BACKEND_URL}/fill-pdf",
            json=debug_form_data,
            headers={
                'Content-Type': 'application/json',
                'X-Debug-Checkboxes': 'true'
            }
        )
        
        if response.status_code == 200:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"debug_checkbox_test_{timestamp}.pdf"
            
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"🎉 Debug PDF generated: {filename}")
            print(f"📄 Size: {len(response.content):,} bytes")
            
            # Count checkboxes by group
            for group_name, checkboxes in debug_form_data.items():
                if isinstance(checkboxes, dict):
                    checked_count = sum(1 for v in checkboxes.values() if _convert_test_value(v))
                    total_count = len(checkboxes)
                    print(f"☑️ {group_name}: {checked_count}/{total_count} checked")
            
            print("\n📊 Check the server logs for detailed checkbox debugging information!")
            
        else:
            print(f"❌ Debug test failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('message', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text[:200]}...")
                
    except Exception as e:
        print(f"❌ Debug test error: {e}")


if __name__ == "__main__":
    print("🚀 Enhanced Checkbox Functionality Test Suite")
    print("Make sure the Flask server is running on http://localhost:5000")
    print()
    
    # Run all tests
    test_checkbox_functionality()
    test_edge_cases()
    test_checkbox_debugging()
    
    print("\n" + "=" * 60)
    print("🏁 CHECKBOX ENHANCEMENT TESTS COMPLETED!")
    print("Check the generated PDFs and server logs for detailed results.")
    print("Look for debug messages about checkbox detection and value setting.")
