# fly.toml app configuration file generated for pdf-form-filler
app = "pdf-form-filler"
primary_region = "sjc"

[build]

[env]
  FLASK_ENV = "production"
  PYTHONPATH = "/app"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[http_service.checks]]
  grace_period = "10s"
  interval = "30s"
  method = "GET"
  timeout = "5s"
  path = "/health"

[vm]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 512

[[mounts]]
  source = "pdf_storage"
  destination = "/app/uploads"
