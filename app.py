from flask import Flask, request, jsonify, send_file
from pdfrw import Pdf<PERSON>ead<PERSON>, PdfWriter, PdfDict
import json
import io
import os
from datetime import datetime

app = Flask(__name__)

# Configuration
PDF_TEMPLATE_PATH = 'as_is_contract_fillable.pdf'
FIELD_MAPPING_PATH = 'field_mapping.json'

# Load or create field mapping
def load_field_mapping():
    if os.path.exists(FIELD_MAPPING_PATH):
        with open(FIELD_MAPPING_PATH, 'r') as f:
            return json.load(f)
    else:
        # Initialize with empty mapping
        return {
            "fields": {},
            "checkbox_groups": {}
        }

def save_field_mapping(mapping):
    with open(FIELD_MAPPING_PATH, 'w') as f:
        json.dump(mapping, f, indent=2)

# Extract all fields from PDF for mapping
@app.route('/extract-fields', methods=['GET'])
def extract_fields():
    try:
        template_pdf = PdfReader(PDF_TEMPLATE_PATH)
        fields = {}
        
        for page_num, page in enumerate(template_pdf.pages):
            if page.Annots:
                for annot in page.Annots:
                    if annot.T and annot.Subtype == '/Widget':
                        field_name = annot.T[1:-1] if annot.T.startswith('(') else annot.T
                        fields[field_name] = {
                            'type': annot.FT[1:] if annot.FT else 'unknown',
                            'page': page_num + 1,
                            'rect': annot.Rect if annot.Rect else None
                        }
        
        return jsonify({
            "status": "success",
            "fields": fields,
            "total_fields": len(fields)
        })
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

# API endpoint to update field mapping
@app.route('/update-mapping', methods=['POST'])
def update_mapping():
    try:
        data = request.json
        mapping = load_field_mapping()
        
        # Update field mapping
        if 'field_name' in data and 'pdf_field' in data:
            mapping['fields'][data['field_name']] = data['pdf_field']
        
        # Update checkbox groups
        if 'checkbox_group' in data:
            group_name = data['checkbox_group']['name']
            mapping['checkbox_groups'][group_name] = data['checkbox_group']['fields']
        
        save_field_mapping(mapping)
        return jsonify({"status": "success"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

# Fill PDF with provided data
@app.route('/fill-pdf', methods=['POST'])
def fill_pdf():
    try:
        form_data = request.json
        mapping = load_field_mapping()
        
        # Load PDF template
        template_pdf = PdfReader(PDF_TEMPLATE_PATH)
        
        # Process regular fields
        for field_name, pdf_field in mapping['fields'].items():
            if field_name in form_data:
                set_pdf_field(template_pdf, pdf_field, form_data[field_name])
        
        # Process checkbox groups
        for group_name, fields in mapping['checkbox_groups'].items():
            if group_name in form_data:
                for option, pdf_field in fields.items():
                    value = form_data[group_name].get(option, False)
                    set_pdf_field(template_pdf, pdf_field, value, is_checkbox=True)
        
        # Save filled PDF to memory
        output_buffer = io.BytesIO()
        PdfWriter().write(output_buffer, template_pdf)
        output_buffer.seek(0)
        
        return send_file(
            output_buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f'filled_contract_{datetime.now().strftime("%Y%m%d")}.pdf'
        )
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

def set_pdf_field(template_pdf, field_name, value, is_checkbox=False):
    for page in template_pdf.pages:
        if page.Annots:
            for annot in page.Annots:
                if annot.Subtype == '/Widget' and annot.T and annot.T[1:-1] == field_name:
                    if is_checkbox:
                        annot.V = '/Yes' if value else '/Off'
                        annot.AS = annot.V
                    else:
                        annot.V = str(value)
                        annot.AP = ''  # Clear appearance to force regeneration

# Get current field mapping
@app.route('/get-mapping', methods=['GET'])
def get_mapping():
    return jsonify(load_field_mapping())

# Health check endpoint
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy"})

if __name__ == '__main__':
    # Create field mapping file if it doesn't exist
    if not os.path.exists(FIELD_MAPPING_PATH):
        save_field_mapping({
            "fields": {},
            "checkbox_groups": {}
        })
    
    app.run(host='0.0.0.0', port=5000, debug=True)