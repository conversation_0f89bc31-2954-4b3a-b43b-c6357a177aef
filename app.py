from flask import Flask, request, jsonify, send_file
from pdfrw import Pdf<PERSON><PERSON><PERSON>, PdfWriter, PdfDict, PdfName
import json
import io
import os
import logging
import re
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass

# Configure logging with enhanced checkbox debugging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    handlers=[
        logging.FileHandler('pdf_filler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Set debug level for checkbox operations when needed
checkbox_logger = logging.getLogger(__name__ + '.checkbox')
checkbox_logger.setLevel(logging.DEBUG)

app = Flask(__name__)

# Enable CORS for all routes
try:
    from flask_cors import CORS
    CORS(app, origins=["*"])  # Configure origins as needed for production
except ImportError:
    # CORS not available, add manual headers
    @app.after_request
    def after_request(response):
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-Debug-Checkboxes')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
        return response

# Configuration
PDF_TEMPLATE_PATH = 'as_is_contract_fillable.pdf'
FIELD_MAPPING_PATH = 'field_mapping.json'
PDF_FIELDS_PATH = 'pdf_fields.json'
FIELD_MAPPING_GUIDE_PATH = 'field_mapping_guide.json'

@dataclass
class PDFFieldInfo:
    """Data class for PDF field information"""
    name: str
    field_type: str
    current_value: str
    page: int
    normalized_name: str = None

    def __post_init__(self):
        if self.normalized_name is None:
            self.normalized_name = self.normalize_field_name(self.name)

    @staticmethod
    def normalize_field_name(field_name: str) -> str:
        """Normalize PDF field names by removing quotes and parentheses"""
        if field_name.startswith("'(") and field_name.endswith(")'"):
            return field_name[2:-2]
        elif field_name.startswith("(") and field_name.endswith(")"):
            return field_name[1:-1]
        return field_name


class PDFFieldProcessor:
    """Enhanced PDF field processor with robust error handling and field type support"""

    def __init__(self, pdf_template_path: str, pdf_fields_path: str):
        self.pdf_template_path = pdf_template_path
        self.pdf_fields_path = pdf_fields_path
        self.pdf_fields_info = self._load_pdf_fields_info()
        self.field_type_handlers = {
            '/Tx': self._handle_text_field,
            '/Btn': self._handle_button_field,
            '/Sig': self._handle_signature_field,
            '/Ch': self._handle_choice_field
        }

    def _load_pdf_fields_info(self) -> Dict[str, PDFFieldInfo]:
        """Load PDF fields information from JSON file"""
        try:
            with open(self.pdf_fields_path, 'r') as f:
                fields_data = json.load(f)

            fields_info = {}
            for field_name, field_data in fields_data.items():
                fields_info[field_name] = PDFFieldInfo(
                    name=field_data['name'],
                    field_type=field_data['type'],
                    current_value=field_data.get('current_value', ''),
                    page=field_data['page']
                )

            logger.info(f"Loaded {len(fields_info)} PDF field definitions")
            return fields_info

        except FileNotFoundError:
            logger.error(f"PDF fields file not found: {self.pdf_fields_path}")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in PDF fields file: {e}")
            return {}
        except Exception as e:
            logger.error(f"Error loading PDF fields info: {e}")
            return {}

    def get_field_info(self, field_name: str) -> Optional[PDFFieldInfo]:
        """Get field information by name (supports both normalized and original names)"""
        # Try exact match first
        if field_name in self.pdf_fields_info:
            return self.pdf_fields_info[field_name]

        # Try normalized name match
        for field_info in self.pdf_fields_info.values():
            if field_info.normalized_name == field_name:
                return field_info

        return None

    def _handle_text_field(self, annot, value: Any) -> bool:
        """Handle text field types (/Tx)"""
        try:
            if value is None or value == '':
                annot.V = ''
            else:
                # Handle different value types
                if isinstance(value, (int, float)):
                    annot.V = str(value)
                elif isinstance(value, str):
                    annot.V = value
                else:
                    annot.V = str(value)

            # Clear appearance to force regeneration
            annot.AP = ''
            return True
        except Exception as e:
            logger.error(f"Error handling text field: {e}")
            return False

    def _handle_button_field(self, annot, value: Any) -> bool:
        """Enhanced checkbox handler that preserves original appearance structure"""
        try:
            # Verify this is a checkbox widget
            if not (annot.Subtype == '/Widget' and annot.FT == '/Btn'):
                return False

            field_name = getattr(annot, 'T', 'unknown_field')
            logger.debug(f"Processing checkbox field: {field_name}")

            # Convert input to boolean
            checkbox_value = self._convert_to_bool(value)
            logger.debug(f"Converted value {value} to boolean: {checkbox_value}")

            # Analyze the existing appearance structure to determine correct states
            on_state, off_state = self._analyze_existing_appearance_states(annot)
            logger.debug(f"Detected appearance states - On: {on_state}, Off: {off_state}")

            # Set the checkbox value and appearance state
            if checkbox_value:
                # Set to checked state
                if on_state:
                    annot.V = PdfName(on_state[1:] if on_state.startswith('/') else on_state)
                    annot.AS = PdfName(on_state[1:] if on_state.startswith('/') else on_state)
                    logger.debug(f"Set checkbox {field_name} to CHECKED ({on_state})")
                else:
                    # Fallback if no on_state detected
                    annot.V = PdfName('Yes')
                    annot.AS = PdfName('Yes')
                    logger.debug(f"Set checkbox {field_name} to CHECKED (fallback /Yes)")
            else:
                # Set to unchecked state
                if off_state:
                    annot.V = PdfName(off_state[1:] if off_state.startswith('/') else off_state)
                    annot.AS = PdfName(off_state[1:] if off_state.startswith('/') else off_state)
                    logger.debug(f"Set checkbox {field_name} to UNCHECKED ({off_state})")
                else:
                    # Fallback if no off_state detected
                    annot.V = PdfName('Off')
                    annot.AS = PdfName('Off')
                    logger.debug(f"Set checkbox {field_name} to UNCHECKED (fallback /Off)")

            logger.info(f"Successfully set checkbox {field_name} to {'checked' if checkbox_value else 'unchecked'}")
            return True

        except Exception as e:
            field_name = getattr(annot, 'T', 'unknown_field')
            logger.error(f"Error setting checkbox {field_name}: {str(e)}")
            return False

    def _convert_to_bool(self, value: Any) -> bool:
        """Convert various input types to boolean"""
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on', 'checked', 'selected', 'y', 't')
        if isinstance(value, (int, float)):
            return bool(value) and value != 0
        return False

    def _analyze_existing_appearance_states(self, annot) -> tuple:
        """Analyze existing appearance dictionary to determine correct on/off states"""
        field_name = getattr(annot, 'T', 'unknown')
        on_state = None
        off_state = None

        try:
            # Check if appearance dictionary exists
            if hasattr(annot, 'AP') and annot.AP and hasattr(annot.AP, 'N') and annot.AP.N:
                ap_keys = list(annot.AP.N.keys()) if hasattr(annot.AP.N, 'keys') else []
                logger.debug(f"Field {field_name} existing appearance keys: {ap_keys}")

                # Look for the field-specific appearance key (like /Checkbox_1)
                field_key = None
                for key in ap_keys:
                    key_str = str(key)
                    if field_name.replace('(', '').replace(')', '') in key_str:
                        field_key = key_str
                        break

                # Determine on/off states based on existing keys
                if '/Off' in [str(k) for k in ap_keys]:
                    off_state = '/Off'

                if field_key:
                    # Use the field-specific key as the "on" state
                    on_state = field_key
                    logger.debug(f"Field {field_name} using field-specific on state: {on_state}")
                elif '/Yes' in [str(k) for k in ap_keys]:
                    on_state = '/Yes'
                elif '/On' in [str(k) for k in ap_keys]:
                    on_state = '/On'
                elif '/1' in [str(k) for k in ap_keys]:
                    on_state = '/1'

                logger.debug(f"Field {field_name} detected states - On: {on_state}, Off: {off_state}")

            # Fallback to standard states if nothing detected
            if not on_state:
                on_state = '/Yes'
            if not off_state:
                off_state = '/Off'

        except Exception as e:
            logger.warning(f"Error analyzing appearance states for {field_name}: {e}")
            on_state = '/Yes'
            off_state = '/Off'

        return on_state, off_state

    def _determine_checkbox_states(self, annot) -> tuple:
        """Determine the correct on/off states for this checkbox"""
        # Default states
        on_state = '/Yes'
        off_state = '/Off'

        field_name = getattr(annot, 'T', 'unknown')

        try:
            # Check for alternative states in appearance dictionary
            if hasattr(annot, 'AP') and annot.AP:
                # Check normal appearance first
                if hasattr(annot.AP, 'N') and annot.AP.N:
                    ap_keys = list(annot.AP.N.keys()) if hasattr(annot.AP.N, 'keys') else []
                    logger.debug(f"Field {field_name} appearance keys: {ap_keys}")

                    # Look for common checkbox states
                    if '/On' in ap_keys:
                        on_state = '/On'
                        logger.debug(f"Field {field_name} using /On state")
                    elif '/1' in ap_keys:
                        on_state = '/1'
                        off_state = '/0'
                        logger.debug(f"Field {field_name} using numeric states")

                # Check down appearance as fallback
                elif hasattr(annot.AP, 'D') and annot.AP.D:
                    ap_keys = list(annot.AP.D.keys()) if hasattr(annot.AP.D, 'keys') else []
                    if '/On' in ap_keys:
                        on_state = '/On'
                        logger.debug(f"Field {field_name} using /On state from down appearance")

            # Check current value for hints
            current_v = getattr(annot, 'V', None)
            if current_v and str(current_v) in ['/On', '/1']:
                if str(current_v) == '/On':
                    on_state = '/On'
                elif str(current_v) == '/1':
                    on_state = '/1'
                    off_state = '/0'
                logger.debug(f"Field {field_name} states determined from current value: {current_v}")

        except Exception as e:
            logger.warning(f"Error determining states for {field_name}, using defaults: {e}")

        return on_state, off_state

    def _ensure_appearance_dict(self, annot, on_state: str, off_state: str):
        """Create and configure appearance dictionary for proper rendering"""
        field_name = getattr(annot, 'T', 'unknown')

        try:
            # Create appearance dictionary if missing
            if not hasattr(annot, 'AP') or not annot.AP:
                logger.debug(f"Creating appearance dictionary for {field_name}")
                annot.AP = PdfDict()

            # Ensure normal appearance dictionary exists
            if not hasattr(annot.AP, 'N') or not annot.AP.N:
                logger.debug(f"Creating normal appearance dictionary for {field_name}")
                annot.AP.N = PdfDict()

            # Add checkbox appearance states using PdfName objects
            # Remove leading '/' if present since PdfName will add it
            on_name_str = on_state[1:] if on_state.startswith('/') else on_state
            off_name_str = off_state[1:] if off_state.startswith('/') else off_state
            on_name = PdfName(on_name_str)
            off_name = PdfName(off_name_str)

            if on_name not in annot.AP.N:
                logger.debug(f"Adding {on_state} appearance state for {field_name}")
                annot.AP.N[on_name] = PdfDict()

            if off_name not in annot.AP.N:
                logger.debug(f"Adding {off_state} appearance state for {field_name}")
                annot.AP.N[off_name] = PdfDict()

            # Ensure down appearance exists (for mouse press feedback)
            if not hasattr(annot.AP, 'D') or not annot.AP.D:
                annot.AP.D = PdfDict()
                annot.AP.D[on_name] = PdfDict()
                annot.AP.D[off_name] = PdfDict()

        except Exception as e:
            logger.warning(f"Could not ensure appearance dictionary for {field_name}: {e}")

    def _analyze_checkbox_field(self, annot) -> Dict[str, Any]:
        """Analyze a button field to determine checkbox characteristics"""
        field_name = getattr(annot, 'T', 'unknown_field')
        analysis = {
            'is_checkbox': False,
            'on_state': '/Yes',
            'off_state': '/Off',
            'has_appearance_dict': False,
            'detection_method': 'none'
        }

        try:
            # Method 1: Check appearance dictionary for checkbox states
            if hasattr(annot, 'AP') and annot.AP:
                analysis['has_appearance_dict'] = True
                logger.debug(f"Field {field_name} has appearance dictionary")

                # Check normal appearance states
                if hasattr(annot.AP, 'N') and annot.AP.N:
                    ap_normal = annot.AP.N
                    logger.debug(f"Field {field_name} normal appearance keys: {list(ap_normal.keys()) if hasattr(ap_normal, 'keys') else 'no keys'}")

                    # Look for common checkbox states
                    if '/On' in ap_normal:
                        analysis['on_state'] = '/On'
                        analysis['is_checkbox'] = True
                        analysis['detection_method'] = 'appearance_on'
                        logger.debug(f"Field {field_name} detected as checkbox via /On state")
                    elif '/Yes' in ap_normal:
                        analysis['on_state'] = '/Yes'
                        analysis['is_checkbox'] = True
                        analysis['detection_method'] = 'appearance_yes'
                        logger.debug(f"Field {field_name} detected as checkbox via /Yes state")
                    elif '/1' in ap_normal:
                        analysis['on_state'] = '/1'
                        analysis['is_checkbox'] = True
                        analysis['detection_method'] = 'appearance_numeric'
                        logger.debug(f"Field {field_name} detected as checkbox via /1 state")

                # Check down appearance states as fallback
                if not analysis['is_checkbox'] and hasattr(annot.AP, 'D') and annot.AP.D:
                    ap_down = annot.AP.D
                    if '/On' in ap_down:
                        analysis['on_state'] = '/On'
                        analysis['is_checkbox'] = True
                        analysis['detection_method'] = 'appearance_down_on'
                        logger.debug(f"Field {field_name} detected as checkbox via down appearance /On")
                    elif '/Yes' in ap_down:
                        analysis['on_state'] = '/Yes'
                        analysis['is_checkbox'] = True
                        analysis['detection_method'] = 'appearance_down_yes'
                        logger.debug(f"Field {field_name} detected as checkbox via down appearance /Yes")

            # Method 2: Check field flags for checkbox characteristics
            if not analysis['is_checkbox'] and hasattr(annot, 'Ff') and annot.Ff:
                # Button field flags: bit 15 = radio button, bit 16 = pushbutton
                # If neither is set, it's likely a checkbox
                flags = int(annot.Ff)
                is_radio = bool(flags & (1 << 15))
                is_pushbutton = bool(flags & (1 << 16))

                if not is_radio and not is_pushbutton:
                    analysis['is_checkbox'] = True
                    analysis['detection_method'] = 'field_flags'
                    logger.debug(f"Field {field_name} detected as checkbox via field flags")

            # Method 3: Check field name patterns (fallback)
            if not analysis['is_checkbox']:
                field_name_lower = str(field_name).lower()
                checkbox_indicators = ['check', 'box', 'tick', 'mark', 'select', 'option']

                for indicator in checkbox_indicators:
                    if indicator in field_name_lower:
                        analysis['is_checkbox'] = True
                        analysis['detection_method'] = f'name_pattern_{indicator}'
                        logger.warning(f"Field {field_name} assumed to be checkbox based on name pattern: {indicator}")
                        break

            # Method 4: Check current value for checkbox patterns
            if not analysis['is_checkbox'] and hasattr(annot, 'V') and annot.V:
                current_value = str(annot.V)
                if current_value in ['/Yes', '/No', '/On', '/Off', '/1', '/0']:
                    analysis['is_checkbox'] = True
                    if current_value in ['/Yes', '/On', '/1']:
                        analysis['on_state'] = current_value
                    analysis['detection_method'] = 'current_value_pattern'
                    logger.debug(f"Field {field_name} detected as checkbox via current value: {current_value}")

            logger.debug(f"Checkbox analysis for {field_name}: {analysis}")
            return analysis

        except Exception as e:
            logger.error(f"Error analyzing checkbox field {field_name}: {e}")
            return analysis

    def _convert_to_boolean(self, value: Any) -> bool:
        """Convert various input types to boolean for checkbox handling"""
        if isinstance(value, bool):
            return value
        elif isinstance(value, str):
            value_lower = value.lower().strip()
            return value_lower in ('true', '1', 'yes', 'on', 'checked', 'selected', 'y', 't')
        elif isinstance(value, (int, float)):
            return bool(value) and value != 0
        else:
            return bool(value)

    def _set_checkbox_value(self, annot, checkbox_value: bool, checkbox_info: Dict[str, Any]) -> bool:
        """Set checkbox value with proper state handling and fallback mechanisms"""
        field_name = getattr(annot, 'T', 'unknown_field')

        try:
            on_state = checkbox_info['on_state']
            off_state = checkbox_info['off_state']

            # Set the primary values
            if checkbox_value:
                target_v = on_state
                target_as = on_state
            else:
                target_v = off_state
                target_as = off_state

            # Set the field value and appearance state
            annot.V = target_v
            annot.AS = target_as

            # Ensure appearance dictionary exists and is properly structured
            self._ensure_checkbox_appearance_dict(annot, checkbox_info)

            # Verify the values were set correctly
            actual_v = getattr(annot, 'V', None)
            actual_as = getattr(annot, 'AS', None)

            if actual_v == target_v and actual_as == target_as:
                logger.debug(f"Successfully set checkbox {field_name}: V={actual_v}, AS={actual_as}")
                return True
            else:
                logger.warning(f"Checkbox {field_name} values not set correctly. Expected V={target_v}, AS={target_as}, Got V={actual_v}, AS={actual_as}")
                return self._fallback_checkbox_setting(annot, checkbox_value)

        except Exception as e:
            logger.error(f"Error setting checkbox value for {field_name}: {e}")
            return self._fallback_checkbox_setting(annot, checkbox_value)

    def _ensure_checkbox_appearance_dict(self, annot, checkbox_info: Dict[str, Any]):
        """Ensure checkbox has proper appearance dictionary structure"""
        field_name = getattr(annot, 'T', 'unknown_field')

        try:
            # Create appearance dictionary if missing
            if not hasattr(annot, 'AP') or not annot.AP:
                logger.debug(f"Creating appearance dictionary for {field_name}")
                annot.AP = PdfDict()

            # Ensure normal appearance dictionary exists
            if not hasattr(annot.AP, 'N') or not annot.AP.N:
                logger.debug(f"Creating normal appearance dictionary for {field_name}")
                annot.AP.N = PdfDict()

            # Add standard checkbox appearance states if missing
            on_state = checkbox_info['on_state']
            off_state = checkbox_info['off_state']

            if on_state not in annot.AP.N:
                logger.debug(f"Adding {on_state} appearance state for {field_name}")
                annot.AP.N[on_state] = PdfDict()

            if off_state not in annot.AP.N:
                logger.debug(f"Adding {off_state} appearance state for {field_name}")
                annot.AP.N[off_state] = PdfDict()

        except Exception as e:
            logger.warning(f"Could not ensure appearance dictionary for {field_name}: {e}")

    def _fallback_checkbox_setting(self, annot, checkbox_value: bool) -> bool:
        """Fallback mechanism for setting checkbox values when standard methods fail"""
        field_name = getattr(annot, 'T', 'unknown_field')

        try:
            logger.info(f"Attempting fallback checkbox setting for {field_name}")

            # Try common checkbox value patterns
            fallback_patterns = [
                ('/Yes', '/Off'),
                ('/On', '/Off'),
                ('/1', '/0'),
                ('/True', '/False'),
                ('Yes', 'Off'),
                ('On', 'Off'),
                ('1', '0')
            ]

            for on_val, off_val in fallback_patterns:
                try:
                    if checkbox_value:
                        annot.V = on_val
                        annot.AS = on_val
                    else:
                        annot.V = off_val
                        annot.AS = off_val

                    # Test if the setting worked
                    if getattr(annot, 'V', None) == (on_val if checkbox_value else off_val):
                        logger.info(f"Fallback successful for {field_name} using pattern {on_val}/{off_val}")
                        return True

                except Exception:
                    continue

            # Final fallback - just set basic boolean-like values
            try:
                if checkbox_value:
                    annot.V = '/Yes'
                    if hasattr(annot, 'AS'):
                        annot.AS = '/Yes'
                else:
                    annot.V = '/Off'
                    if hasattr(annot, 'AS'):
                        annot.AS = '/Off'

                logger.warning(f"Used basic fallback for checkbox {field_name}")
                return True

            except Exception as e:
                logger.error(f"All fallback methods failed for {field_name}: {e}")
                return False

        except Exception as e:
            logger.error(f"Fallback checkbox setting failed for {field_name}: {e}")
            return False

    def _handle_signature_field(self, annot, value: Any) -> bool:
        """Handle signature field types (/Sig)"""
        try:
            if value and str(value).strip():
                # For signature fields, we'll set the value as text
                # In a real implementation, you might want to handle actual signature data
                annot.V = str(value)
            else:
                annot.V = ''
            return True
        except Exception as e:
            logger.error(f"Error handling signature field: {e}")
            return False

    def _handle_choice_field(self, annot, value: Any) -> bool:
        """Handle choice field types (/Ch) - dropdowns and listboxes"""
        try:
            if value is not None:
                annot.V = str(value)
            else:
                annot.V = ''
            return True
        except Exception as e:
            logger.error(f"Error handling choice field: {e}")
            return False

    def set_pdf_field(self, template_pdf, field_name: str, value: Any) -> bool:
        """
        Set a PDF field value with enhanced error handling and type support

        Args:
            template_pdf: The PDF template object
            field_name: Name of the field to set
            value: Value to set

        Returns:
            bool: True if field was set successfully, False otherwise
        """
        try:
            # Get field information
            field_info = self.get_field_info(field_name)
            if not field_info:
                logger.warning(f"Field info not found for: {field_name}")
                # Try to set field anyway using original method as fallback
                return self._set_field_fallback(template_pdf, field_name, value)

            # Find and update the field in the PDF
            field_found = False
            for page in template_pdf.pages:
                if not page.Annots:
                    continue

                for annot in page.Annots:
                    if not (annot.Subtype == '/Widget' and annot.T):
                        continue

                    # Normalize annotation field name for comparison
                    annot_field_name = annot.T[1:-1] if annot.T.startswith('(') else annot.T

                    # Check if this is the field we're looking for
                    if (annot_field_name == field_info.normalized_name or
                        annot_field_name == field_name or
                        annot.T == field_info.name):

                        # Get the appropriate handler for this field type
                        handler = self.field_type_handlers.get(field_info.field_type)
                        if handler:
                            success = handler(annot, value)
                            if success:
                                field_found = True
                                logger.debug(f"Successfully set field {field_name} = {value}")
                            else:
                                logger.error(f"Handler failed for field {field_name}")
                        else:
                            logger.warning(f"No handler for field type {field_info.field_type}, using fallback")
                            success = self._set_field_fallback_annotation(annot, value)
                            if success:
                                field_found = True

            if not field_found:
                logger.warning(f"Field not found in PDF: {field_name}")
                return False

            return field_found

        except Exception as e:
            logger.error(f"Error setting PDF field {field_name}: {e}")
            return False

    def _set_field_fallback(self, template_pdf, field_name: str, value: Any) -> bool:
        """Fallback method for setting fields when field info is not available"""
        try:
            for page in template_pdf.pages:
                if not page.Annots:
                    continue

                for annot in page.Annots:
                    if not (annot.Subtype == '/Widget' and annot.T):
                        continue

                    annot_field_name = annot.T[1:-1] if annot.T.startswith('(') else annot.T

                    if annot_field_name == field_name:
                        return self._set_field_fallback_annotation(annot, value)

            return False
        except Exception as e:
            logger.error(f"Error in fallback field setting: {e}")
            return False

    def _set_field_fallback_annotation(self, annot, value: Any) -> bool:
        """Set annotation value using fallback logic"""
        try:
            # Determine if it's a checkbox based on field type
            if annot.FT and annot.FT == '/Btn':
                # Handle as checkbox
                if isinstance(value, bool):
                    checkbox_value = value
                elif isinstance(value, str):
                    checkbox_value = value.lower() in ('true', '1', 'yes', 'on', 'checked')
                else:
                    checkbox_value = bool(value)

                annot.V = '/Yes' if checkbox_value else '/Off'
                annot.AS = annot.V
            else:
                # Handle as text field
                annot.V = str(value) if value is not None else ''
                annot.AP = ''

            return True
        except Exception as e:
            logger.error(f"Error in fallback annotation setting: {e}")
            return False


class FieldMappingManager:
    """Manages field mappings between frontend form data and PDF fields"""

    def __init__(self, field_mapping_path: str, field_mapping_guide_path: str, pdf_fields_path: str):
        self.field_mapping_path = field_mapping_path
        self.field_mapping_guide_path = field_mapping_guide_path
        self.pdf_fields_path = pdf_fields_path
        self.field_mapping = self._load_field_mapping()
        self.field_mapping_guide = self._load_field_mapping_guide()
        self.pdf_fields_info = self._load_pdf_fields_info()

    def _load_field_mapping(self) -> Dict[str, Any]:
        """Load field mapping configuration"""
        try:
            if os.path.exists(self.field_mapping_path):
                with open(self.field_mapping_path, 'r') as f:
                    mapping = json.load(f)
                logger.info(f"Loaded field mapping with {len(mapping.get('fields', {}))} fields")
                return mapping
            else:
                logger.info("Field mapping file not found, creating default structure")
                return {"fields": {}, "checkbox_groups": {}}
        except Exception as e:
            logger.error(f"Error loading field mapping: {e}")
            return {"fields": {}, "checkbox_groups": {}}

    def _load_field_mapping_guide(self) -> Dict[str, Any]:
        """Load field mapping guide for reference"""
        try:
            if os.path.exists(self.field_mapping_guide_path):
                with open(self.field_mapping_guide_path, 'r') as f:
                    guide = json.load(f)
                logger.info("Loaded field mapping guide")
                return guide
            else:
                logger.warning("Field mapping guide not found")
                return {}
        except Exception as e:
            logger.error(f"Error loading field mapping guide: {e}")
            return {}

    def _load_pdf_fields_info(self) -> Dict[str, Any]:
        """Load PDF fields information"""
        try:
            if os.path.exists(self.pdf_fields_path):
                with open(self.pdf_fields_path, 'r') as f:
                    fields = json.load(f)
                logger.info(f"Loaded {len(fields)} PDF field definitions")
                return fields
            else:
                logger.warning("PDF fields info file not found")
                return {}
        except Exception as e:
            logger.error(f"Error loading PDF fields info: {e}")
            return {}

    def save_field_mapping(self, mapping: Dict[str, Any]) -> bool:
        """Save field mapping to file"""
        try:
            with open(self.field_mapping_path, 'w') as f:
                json.dump(mapping, f, indent=2)
            self.field_mapping = mapping
            logger.info("Field mapping saved successfully")
            return True
        except Exception as e:
            logger.error(f"Error saving field mapping: {e}")
            return False

    def get_pdf_field_for_form_field(self, form_field_name: str) -> Optional[str]:
        """Get PDF field name for a given form field name"""
        # Check direct mapping first
        if form_field_name in self.field_mapping.get('fields', {}):
            return self.field_mapping['fields'][form_field_name]

        # Check field mapping guide
        if self.field_mapping_guide:
            for category, fields in self.field_mapping_guide.get('contract_fields', {}).items():
                if isinstance(fields, dict) and form_field_name in fields:
                    pdf_field = fields[form_field_name]
                    # Normalize the PDF field name to match our PDF fields format
                    normalized_pdf_field = f"'({pdf_field})'"
                    if normalized_pdf_field in self.pdf_fields_info:
                        return normalized_pdf_field
                    return pdf_field

        return None

    def get_checkbox_group_mapping(self, group_name: str) -> Dict[str, str]:
        """Get checkbox group mapping"""
        return self.field_mapping.get('checkbox_groups', {}).get(group_name, {})

    def validate_form_data(self, form_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Validate form data against available PDF fields

        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        validation_result = {'errors': [], 'warnings': []}

        try:
            for field_name, value in form_data.items():
                # Skip None values
                if value is None:
                    continue

                # Check if it's a checkbox group
                if isinstance(value, dict):
                    checkbox_mapping = self.get_checkbox_group_mapping(field_name)
                    if not checkbox_mapping:
                        validation_result['warnings'].append(
                            f"No checkbox group mapping found for: {field_name}"
                        )
                    else:
                        for option_name in value.keys():
                            if option_name not in checkbox_mapping:
                                validation_result['warnings'].append(
                                    f"No mapping for checkbox option: {field_name}.{option_name}"
                                )
                else:
                    # Regular field
                    pdf_field = self.get_pdf_field_for_form_field(field_name)
                    if not pdf_field:
                        validation_result['warnings'].append(
                            f"No PDF field mapping found for: {field_name}"
                        )
                    elif pdf_field not in self.pdf_fields_info:
                        validation_result['errors'].append(
                            f"Mapped PDF field does not exist: {pdf_field} (for form field: {field_name})"
                        )

        except Exception as e:
            validation_result['errors'].append(f"Validation error: {str(e)}")

        return validation_result

    def create_default_mapping_from_guide(self) -> bool:
        """Create default field mapping from the field mapping guide"""
        try:
            if not self.field_mapping_guide:
                logger.error("No field mapping guide available")
                return False

            new_mapping = {"fields": {}, "checkbox_groups": {}}

            # Process contract fields
            contract_fields = self.field_mapping_guide.get('contract_fields', {})

            for category, fields in contract_fields.items():
                if isinstance(fields, dict):
                    if category.endswith('_checkboxes'):
                        # Handle checkbox groups
                        group_name = category.replace('_checkboxes', '')
                        checkbox_mapping = {}
                        for option_name, pdf_field in fields.items():
                            normalized_pdf_field = f"'({pdf_field})'"
                            if normalized_pdf_field in self.pdf_fields_info:
                                checkbox_mapping[option_name] = normalized_pdf_field
                            else:
                                checkbox_mapping[option_name] = pdf_field

                        if checkbox_mapping:
                            new_mapping['checkbox_groups'][group_name] = checkbox_mapping
                    else:
                        # Handle regular fields
                        for form_field, pdf_field in fields.items():
                            normalized_pdf_field = f"'({pdf_field})'"
                            if normalized_pdf_field in self.pdf_fields_info:
                                new_mapping['fields'][form_field] = normalized_pdf_field
                            else:
                                new_mapping['fields'][form_field] = pdf_field

            # Save the new mapping
            success = self.save_field_mapping(new_mapping)
            if success:
                logger.info("Default field mapping created from guide")

            return success

        except Exception as e:
            logger.error(f"Error creating default mapping: {e}")
            return False


class PDFFormFiller:
    """Main class for filling PDF forms with comprehensive error handling and validation"""

    def __init__(self):
        self.pdf_processor = PDFFieldProcessor(PDF_TEMPLATE_PATH, PDF_FIELDS_PATH)
        self.mapping_manager = FieldMappingManager(
            FIELD_MAPPING_PATH,
            FIELD_MAPPING_GUIDE_PATH,
            PDF_FIELDS_PATH
        )

        # Initialize default mapping if none exists
        if not self.mapping_manager.field_mapping.get('fields') and not self.mapping_manager.field_mapping.get('checkbox_groups'):
            logger.info("No field mapping found, creating default from guide")
            self.mapping_manager.create_default_mapping_from_guide()

    def validate_input_data(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Comprehensive input validation

        Returns:
            Dict with validation results including errors, warnings, and processed data
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'processed_data': {}
        }

        try:
            if not form_data:
                validation_result['valid'] = False
                validation_result['errors'].append("No form data provided")
                return validation_result

            # Validate against field mappings
            mapping_validation = self.mapping_manager.validate_form_data(form_data)
            validation_result['errors'].extend(mapping_validation['errors'])
            validation_result['warnings'].extend(mapping_validation['warnings'])

            # Process and validate individual fields
            for field_name, value in form_data.items():
                try:
                    processed_value = self._process_field_value(field_name, value)
                    validation_result['processed_data'][field_name] = processed_value
                except ValueError as e:
                    validation_result['errors'].append(f"Invalid value for field {field_name}: {str(e)}")
                except Exception as e:
                    validation_result['warnings'].append(f"Processing warning for field {field_name}: {str(e)}")
                    validation_result['processed_data'][field_name] = value

            # Set overall validity
            validation_result['valid'] = len(validation_result['errors']) == 0

        except Exception as e:
            validation_result['valid'] = False
            validation_result['errors'].append(f"Validation error: {str(e)}")
            logger.error(f"Input validation error: {e}")

        return validation_result

    def _process_field_value(self, field_name: str, value: Any) -> Any:
        """Process and validate individual field values"""
        if value is None or value == '':
            return ''

        # Handle different field types based on name patterns
        if 'date' in field_name.lower():
            return self._process_date_value(value)
        elif 'phone' in field_name.lower():
            return self._process_phone_value(value)
        elif 'email' in field_name.lower():
            return self._process_email_value(value)
        elif 'number' in field_name.lower() or 'price' in field_name.lower() or 'amount' in field_name.lower():
            return self._process_number_value(value)
        elif isinstance(value, dict):
            # Handle checkbox groups
            return self._process_checkbox_group_value(value)
        else:
            return str(value).strip()

    def _process_date_value(self, value: Any) -> str:
        """Process date values"""
        if isinstance(value, str):
            # Try to parse and reformat date
            import re
            date_patterns = [
                r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
                r'\d{2}/\d{2}/\d{4}',  # MM/DD/YYYY
                r'\d{2}-\d{2}-\d{4}',  # MM-DD-YYYY
            ]

            for pattern in date_patterns:
                if re.match(pattern, value.strip()):
                    return value.strip()

            raise ValueError(f"Invalid date format: {value}")

        return str(value)

    def _process_phone_value(self, value: Any) -> str:
        """Process phone number values"""
        if isinstance(value, str):
            # Remove non-digit characters
            digits = re.sub(r'[^\d]', '', value)
            if len(digits) == 10:
                return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
            elif len(digits) == 11 and digits[0] == '1':
                return f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
            else:
                return value.strip()

        return str(value)

    def _process_email_value(self, value: Any) -> str:
        """Process email values"""
        if isinstance(value, str):
            email = value.strip().lower()
            # Basic email validation
            if '@' in email and '.' in email.split('@')[1]:
                return email
            else:
                raise ValueError(f"Invalid email format: {value}")

        return str(value)

    def _process_number_value(self, value: Any) -> str:
        """Process numeric values"""
        if isinstance(value, (int, float)):
            return str(value)
        elif isinstance(value, str):
            # Remove currency symbols and commas
            cleaned = re.sub(r'[$,]', '', value.strip())
            try:
                float(cleaned)
                return cleaned
            except ValueError:
                raise ValueError(f"Invalid number format: {value}")

        return str(value)

    def _process_checkbox_group_value(self, value: Dict[str, Any]) -> Dict[str, bool]:
        """Process checkbox group values"""
        processed = {}
        for option, checked in value.items():
            if isinstance(checked, bool):
                processed[option] = checked
            elif isinstance(checked, str):
                processed[option] = checked.lower() in ('true', '1', 'yes', 'on', 'checked')
            else:
                processed[option] = bool(checked)

        return processed

    def fill_pdf(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Fill PDF with form data

        Returns:
            Dict with success status, PDF data, errors, and warnings
        """
        result = {
            'success': False,
            'pdf_data': None,
            'errors': [],
            'warnings': [],
            'fields_processed': 0,
            'fields_failed': 0
        }

        try:
            # Validate input data
            validation_result = self.validate_input_data(form_data)
            result['errors'].extend(validation_result['errors'])
            result['warnings'].extend(validation_result['warnings'])

            if not validation_result['valid']:
                logger.error(f"Input validation failed: {validation_result['errors']}")
                return result

            # Load PDF template
            try:
                template_pdf = PdfReader(PDF_TEMPLATE_PATH)
                logger.info(f"Loaded PDF template: {PDF_TEMPLATE_PATH}")
            except Exception as e:
                error_msg = f"Failed to load PDF template: {str(e)}"
                logger.error(error_msg)
                result['errors'].append(error_msg)
                return result

            # Process form data
            processed_data = validation_result['processed_data']

            # Fill regular fields
            for field_name, value in processed_data.items():
                if isinstance(value, dict):
                    # Handle checkbox groups
                    success = self._fill_checkbox_group(template_pdf, field_name, value)
                    if success:
                        result['fields_processed'] += len(value)
                    else:
                        result['fields_failed'] += len(value)
                        result['warnings'].append(f"Failed to process checkbox group: {field_name}")
                else:
                    # Handle regular fields
                    success = self._fill_regular_field(template_pdf, field_name, value)
                    if success:
                        result['fields_processed'] += 1
                    else:
                        result['fields_failed'] += 1
                        result['warnings'].append(f"Failed to process field: {field_name}")

            # Generate PDF output
            try:
                output_buffer = io.BytesIO()
                PdfWriter().write(output_buffer, template_pdf)
                output_buffer.seek(0)
                result['pdf_data'] = output_buffer
                result['success'] = True

                logger.info(f"PDF generated successfully. Processed: {result['fields_processed']}, Failed: {result['fields_failed']}")

            except Exception as e:
                error_msg = f"Failed to generate PDF output: {str(e)}"
                logger.error(error_msg)
                result['errors'].append(error_msg)
                return result

        except Exception as e:
            error_msg = f"Unexpected error during PDF filling: {str(e)}"
            logger.error(error_msg)
            result['errors'].append(error_msg)

        return result

    def _fill_regular_field(self, template_pdf, field_name: str, value: Any) -> bool:
        """Fill a regular PDF field"""
        try:
            # Get PDF field name from mapping
            pdf_field_name = self.mapping_manager.get_pdf_field_for_form_field(field_name)

            if not pdf_field_name:
                logger.warning(f"No PDF field mapping found for: {field_name}")
                return False

            # Set the field value
            success = self.pdf_processor.set_pdf_field(template_pdf, pdf_field_name, value)

            if success:
                logger.debug(f"Successfully filled field: {field_name} -> {pdf_field_name} = {value}")
            else:
                logger.warning(f"Failed to fill field: {field_name} -> {pdf_field_name}")

            return success

        except Exception as e:
            logger.error(f"Error filling regular field {field_name}: {e}")
            return False

    def _fill_checkbox_group(self, template_pdf, group_name: str, checkbox_values: Dict[str, bool]) -> bool:
        """Fill a checkbox group"""
        try:
            checkbox_mapping = self.mapping_manager.get_checkbox_group_mapping(group_name)

            if not checkbox_mapping:
                logger.warning(f"No checkbox group mapping found for: {group_name}")
                return False

            success_count = 0
            total_count = 0

            for option_name, checked in checkbox_values.items():
                total_count += 1

                if option_name in checkbox_mapping:
                    pdf_field_name = checkbox_mapping[option_name]
                    success = self.pdf_processor.set_pdf_field(template_pdf, pdf_field_name, checked)

                    if success:
                        success_count += 1
                        logger.debug(f"Successfully filled checkbox: {group_name}.{option_name} -> {pdf_field_name} = {checked}")
                    else:
                        logger.warning(f"Failed to fill checkbox: {group_name}.{option_name} -> {pdf_field_name}")
                else:
                    logger.warning(f"No mapping found for checkbox option: {group_name}.{option_name}")

            return success_count > 0

        except Exception as e:
            logger.error(f"Error filling checkbox group {group_name}: {e}")
            return False


# Initialize the PDF form filler
pdf_form_filler = PDFFormFiller()

# Legacy functions for backward compatibility
def load_field_mapping():
    """Legacy function - use pdf_form_filler.mapping_manager instead"""
    return pdf_form_filler.mapping_manager.field_mapping

def save_field_mapping(mapping):
    """Legacy function - use pdf_form_filler.mapping_manager instead"""
    return pdf_form_filler.mapping_manager.save_field_mapping(mapping)

# Extract all fields from PDF for mapping
@app.route('/extract-fields', methods=['GET'])
def extract_fields():
    try:
        template_pdf = PdfReader(PDF_TEMPLATE_PATH)
        fields = {}
        
        for page_num, page in enumerate(template_pdf.pages):
            if page.Annots:
                for annot in page.Annots:
                    if annot.T and annot.Subtype == '/Widget':
                        field_name = annot.T[1:-1] if annot.T.startswith('(') else annot.T
                        fields[field_name] = {
                            'type': annot.FT[1:] if annot.FT else 'unknown',
                            'page': page_num + 1,
                            'rect': annot.Rect if annot.Rect else None
                        }
        
        return jsonify({
            "status": "success",
            "fields": fields,
            "total_fields": len(fields)
        })
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

# API endpoint to update field mapping
@app.route('/update-mapping', methods=['POST'])
def update_mapping():
    try:
        data = request.json
        mapping = load_field_mapping()
        
        # Update field mapping
        if 'field_name' in data and 'pdf_field' in data:
            mapping['fields'][data['field_name']] = data['pdf_field']
        
        # Update checkbox groups
        if 'checkbox_group' in data:
            group_name = data['checkbox_group']['name']
            mapping['checkbox_groups'][group_name] = data['checkbox_group']['fields']
        
        save_field_mapping(mapping)
        return jsonify({"status": "success"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

# Fill PDF with provided data
@app.route('/fill-pdf', methods=['POST'])
def fill_pdf():
    """Enhanced PDF filling endpoint with comprehensive error handling"""
    try:
        # Validate request
        if not request.json:
            return jsonify({
                "status": "error",
                "message": "No JSON data provided"
            }), 400

        form_data = request.json
        logger.info(f"Received PDF fill request with {len(form_data)} fields")

        # Check for debug mode
        debug_checkboxes = request.headers.get('X-Debug-Checkboxes', '').lower() == 'true'
        if debug_checkboxes:
            logger.info("Checkbox debug mode enabled")
            # Temporarily enable debug logging for checkbox operations
            checkbox_logger.setLevel(logging.DEBUG)
            # Also enable debug for the main logger temporarily
            original_level = logger.level
            logger.setLevel(logging.DEBUG)

        try:
            # Fill PDF using enhanced system
            result = pdf_form_filler.fill_pdf(form_data)

            if result['success']:
                # Return successful PDF
                logger.info(f"PDF generated successfully. Fields processed: {result['fields_processed']}, Failed: {result['fields_failed']}")

                # Prepare response with warnings if any
                response_data = {
                    "status": "success",
                    "fields_processed": result['fields_processed'],
                    "fields_failed": result['fields_failed']
                }

                if result['warnings']:
                    response_data['warnings'] = result['warnings']

                # Return PDF file
                return send_file(
                    result['pdf_data'],
                    mimetype='application/pdf',
                    as_attachment=True,
                    download_name=f'filled_contract_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
                )
            else:
                # Return error response
                logger.error(f"PDF generation failed: {result['errors']}")
                return jsonify({
                    "status": "error",
                    "message": "PDF generation failed",
                    "errors": result['errors'],
                    "warnings": result['warnings'],
                    "fields_processed": result['fields_processed'],
                    "fields_failed": result['fields_failed']
                }), 500

        finally:
            # Reset logging levels if debug mode was enabled
            if debug_checkboxes:
                checkbox_logger.setLevel(logging.INFO)
                logger.setLevel(original_level)
                logger.info("Checkbox debug mode disabled")

    except Exception as e:
        logger.error(f"Unexpected error in fill_pdf endpoint: {e}")
        return jsonify({
            "status": "error",
            "message": f"Unexpected error: {str(e)}"
        }), 500

# Legacy function for backward compatibility
def set_pdf_field(template_pdf, field_name, value, is_checkbox=False):
    """Legacy function - use PDFFieldProcessor.set_pdf_field instead"""
    return pdf_form_filler.pdf_processor.set_pdf_field(template_pdf, field_name, value)

# Get current field mapping
@app.route('/get-mapping', methods=['GET'])
def get_mapping():
    """Get current field mapping configuration"""
    try:
        mapping = load_field_mapping()
        return jsonify({
            "status": "success",
            "mapping": mapping,
            "total_fields": len(mapping.get('fields', {})),
            "total_checkbox_groups": len(mapping.get('checkbox_groups', {}))
        })
    except Exception as e:
        logger.error(f"Error getting field mapping: {e}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

# Validate form data endpoint
@app.route('/validate-form', methods=['POST'])
def validate_form():
    """Validate form data without generating PDF"""
    try:
        if not request.json:
            return jsonify({
                "status": "error",
                "message": "No JSON data provided"
            }), 400

        form_data = request.json
        validation_result = pdf_form_filler.validate_input_data(form_data)

        return jsonify({
            "status": "success" if validation_result['valid'] else "validation_failed",
            "valid": validation_result['valid'],
            "errors": validation_result['errors'],
            "warnings": validation_result['warnings'],
            "processed_fields": len(validation_result['processed_data'])
        })

    except Exception as e:
        logger.error(f"Error validating form data: {e}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

# Initialize default mapping endpoint
@app.route('/initialize-mapping', methods=['POST'])
def initialize_mapping():
    """Initialize field mapping from guide"""
    try:
        success = pdf_form_filler.mapping_manager.create_default_mapping_from_guide()

        if success:
            mapping = load_field_mapping()
            return jsonify({
                "status": "success",
                "message": "Field mapping initialized from guide",
                "mapping": mapping,
                "total_fields": len(mapping.get('fields', {})),
                "total_checkbox_groups": len(mapping.get('checkbox_groups', {}))
            })
        else:
            return jsonify({
                "status": "error",
                "message": "Failed to initialize field mapping"
            }), 500

    except Exception as e:
        logger.error(f"Error initializing field mapping: {e}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

# Test checkbox functionality endpoint
@app.route('/test-checkboxes', methods=['POST'])
def test_checkboxes():
    """Test endpoint specifically for checkbox functionality debugging"""
    try:
        if not request.json:
            return jsonify({
                "status": "error",
                "message": "No JSON data provided"
            }), 400

        form_data = request.json

        # Enable debug logging for this test
        original_level = logger.level
        logger.setLevel(logging.DEBUG)
        checkbox_logger.setLevel(logging.DEBUG)

        try:
            logger.info("Starting checkbox functionality test")

            # Analyze checkbox data in the request
            checkbox_analysis = {}
            total_checkboxes = 0

            for field_name, value in form_data.items():
                if isinstance(value, dict):
                    # This is likely a checkbox group
                    checkbox_analysis[field_name] = {
                        "total": len(value),
                        "checked": 0,
                        "values": {}
                    }

                    for option, option_value in value.items():
                        converted_value = pdf_form_filler._process_checkbox_group_value({option: option_value})[option]
                        checkbox_analysis[field_name]["values"][option] = {
                            "original": option_value,
                            "converted": converted_value
                        }
                        if converted_value:
                            checkbox_analysis[field_name]["checked"] += 1
                        total_checkboxes += 1

            # Validate the form data
            validation_result = pdf_form_filler.validate_input_data(form_data)

            return jsonify({
                "status": "success",
                "checkbox_analysis": checkbox_analysis,
                "total_checkboxes": total_checkboxes,
                "validation": {
                    "valid": validation_result['valid'],
                    "errors": validation_result['errors'],
                    "warnings": validation_result['warnings'],
                    "processed_fields": len(validation_result['processed_data'])
                }
            })

        finally:
            # Reset logging levels
            logger.setLevel(original_level)
            checkbox_logger.setLevel(logging.INFO)

    except Exception as e:
        logger.error(f"Error in checkbox test endpoint: {e}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

# Debug checkbox functionality endpoint
@app.route('/debug-checkboxes', methods=['GET'])
def debug_checkboxes():
    """Special endpoint for comprehensive checkbox debugging"""
    try:
        template_pdf = PdfReader(PDF_TEMPLATE_PATH)

        # Process all checkbox fields
        checkbox_report = []
        total_checkboxes = 0

        for page_num, page in enumerate(template_pdf.pages, 1):
            if not page.Annots:
                continue

            for annot in page.Annots:
                if annot.Subtype == '/Widget' and annot.FT == '/Btn':
                    total_checkboxes += 1
                    field_name = annot.T[1:-1] if annot.T.startswith('(') else annot.T

                    # Analyze checkbox properties
                    current_value = getattr(annot, 'V', 'None')
                    current_as = getattr(annot, 'AS', 'None')
                    has_ap = hasattr(annot, 'AP') and annot.AP is not None

                    ap_info = {}
                    if has_ap:
                        if hasattr(annot.AP, 'N') and annot.AP.N:
                            ap_info['normal_keys'] = list(annot.AP.N.keys()) if hasattr(annot.AP.N, 'keys') else []
                        if hasattr(annot.AP, 'D') and annot.AP.D:
                            ap_info['down_keys'] = list(annot.AP.D.keys()) if hasattr(annot.AP.D, 'keys') else []

                    # Check if field is in our mapping
                    in_mapping = False
                    mapped_to = None
                    for group_name, group_mapping in pdf_form_filler.mapping_manager.field_mapping.get('checkbox_groups', {}).items():
                        for option_name, pdf_field in group_mapping.items():
                            if pdf_field == field_name or pdf_field.strip("'()") == field_name:
                                in_mapping = True
                                mapped_to = f"{group_name}.{option_name}"
                                break
                        if in_mapping:
                            break

                    checkbox_report.append({
                        'name': field_name,
                        'page': page_num,
                        'current_value': str(current_value),
                        'appearance_state': str(current_as),
                        'has_appearance_dict': has_ap,
                        'appearance_info': ap_info,
                        'in_mapping': in_mapping,
                        'mapped_to': mapped_to,
                        'is_checked': current_value in ['/Yes', '/On', '/1', '(/Yes)', '(/On)', '(/1)']
                    })

        return jsonify({
            "status": "success",
            "total_checkboxes": total_checkboxes,
            "checkboxes": checkbox_report,
            "field_mapping": pdf_form_filler.mapping_manager.field_mapping.get('checkbox_groups', {}),
            "summary": {
                "mapped_checkboxes": sum(1 for cb in checkbox_report if cb['in_mapping']),
                "unmapped_checkboxes": sum(1 for cb in checkbox_report if not cb['in_mapping']),
                "currently_checked": sum(1 for cb in checkbox_report if cb['is_checked'])
            }
        })

    except Exception as e:
        logger.error(f"Error in debug-checkboxes endpoint: {e}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

# Health check endpoint
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy"})

if __name__ == '__main__':
    # Create field mapping file if it doesn't exist
    if not os.path.exists(FIELD_MAPPING_PATH):
        save_field_mapping({
            "fields": {},
            "checkbox_groups": {}
        })

    # Production configuration
    import os
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') != 'production'

    app.run(host='0.0.0.0', port=port, debug=debug)