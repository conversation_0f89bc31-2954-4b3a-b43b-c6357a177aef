import streamlit as st
import requests
import json
from io import BytesIO

# Configuration
BACKEND_URL = "http://localhost:5000"  # Change if your Flask backend is elsewhere
PDF_TEMPLATE = "as_is_contract_fillable.pdf"  # Your PDF template filename

st.set_page_config(page_title="PDF Form Filler", layout="wide")

def main():
    st.title("📝 AS IS Contract PDF Filler")
    st.subheader("Test Interface for Flask Backend")

    # Initialize session state for mapping
    if 'field_mapping' not in st.session_state:
        st.session_state.field_mapping = {"fields": {}, "checkbox_groups": {}}
    
    if 'pdf_fields' not in st.session_state:
        st.session_state.pdf_fields = {}

    # Sidebar for admin functions
    with st.sidebar:
        st.header("Administration")
        if st.button("🔄 Extract PDF Fields"):
            with st.spinner("Extracting fields from PDF..."):
                try:
                    response = requests.get(f"{BACKEND_URL}/extract-fields")
                    if response.status_code == 200:
                        st.session_state.pdf_fields = response.json()['fields']
                        st.success(f"Found {len(st.session_state.pdf_fields)} fields!")
                    else:
                        st.error(f"Error: {response.text}")
                except requests.exceptions.RequestException as e:
                    st.error(f"Connection error: {str(e)}")

        st.download_button(
            "💾 Download Current Mapping",
            data=json.dumps(st.session_state.field_mapping, indent=2),
            file_name="field_mapping.json",
            mime="application/json"
        )

        uploaded_mapping = st.file_uploader("📤 Upload Mapping JSON", type=['json'])
        if uploaded_mapping:
            try:
                st.session_state.field_mapping = json.load(uploaded_mapping)
                st.success("Mapping loaded successfully!")
            except Exception as e:
                st.error(f"Error loading mapping: {str(e)}")

    # Main interface tabs
    tab1, tab2, tab3 = st.tabs(["📋 Form Filler", "🛠️ Field Mapper", "⚙️ API Tester"])

    with tab1:  # Form Filler
        st.header("Fill Out Contract Form")
        
        form_data = {}
        col1, col2 = st.columns(2)
        
        with col1:
            # Basic info section
            st.subheader("Parties")
            form_data['seller_name'] = st.text_input("Seller Name", key="seller_name")
            form_data['buyer_name'] = st.text_input("Buyer Name", key="buyer_name")
            
            st.subheader("Property Details")
            form_data['street_address'] = st.text_input("Street Address", key="street_address")
            form_data['county'] = st.text_input("County", key="county")
            form_data['tax_id'] = st.text_input("Tax ID", key="tax_id")
            
        with col2:
            # Financial section
            st.subheader("Financial Terms")
            form_data['purchase_price'] = st.number_input("Purchase Price", min_value=0, step=1000, key="purchase_price")
            form_data['initial_deposit_amount'] = st.number_input("Initial Deposit", min_value=0, key="initial_deposit")
            
            st.subheader("Financing")
            financing_type = st.radio("Financing Type", 
                                    ["Cash", "Conventional", "FHA", "VA"], 
                                    key="financing_type")
            
            form_data['financing_type'] = {
                "cash": financing_type == "Cash",
                "conventional": financing_type == "Conventional",
                "fha": financing_type == "FHA",
                "va": financing_type == "VA"
            }
        
        # Form submission
        if st.button("Generate Filled PDF", type="primary"):
            with st.spinner("Generating PDF..."):
                try:
                    response = requests.post(
                        f"{BACKEND_URL}/fill-pdf",
                        json=form_data,
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response.status_code == 200:
                        st.success("PDF generated successfully!")
                        pdf_bytes = BytesIO(response.content)
                        st.download_button(
                            "⬇️ Download Filled PDF",
                            data=pdf_bytes,
                            file_name="filled_contract.pdf",
                            mime="application/pdf"
                        )
                    else:
                        st.error(f"Error generating PDF: {response.text}")
                except requests.exceptions.RequestException as e:
                    st.error(f"Connection error: {str(e)}")

    with tab2:  # Field Mapper
        st.header("PDF Field Mapping Tool")
        
        if not st.session_state.pdf_fields:
            st.warning("No PDF fields loaded. Click 'Extract PDF Fields' in the sidebar first.")
        else:
            st.info(f"Loaded {len(st.session_state.pdf_fields)} fields from PDF")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Available PDF Fields")
                selected_field = st.selectbox(
                    "Select PDF Field",
                    options=list(st.session_state.pdf_fields.keys()),
                    format_func=lambda x: f"{x} ({st.session_state.pdf_fields[x]['type']})"
                )
                
                if selected_field:
                    field_info = st.session_state.pdf_fields[selected_field]
                    st.json(field_info)
            
            with col2:
                st.subheader("Map to Application Field")
                mapping_type = st.radio("Field Type", ["Regular Field", "Checkbox Group"])
                
                if mapping_type == "Regular Field":
                    app_field_name = st.text_input("Application Field Name")
                    if st.button("Save Regular Field Mapping"):
                        if app_field_name:
                            st.session_state.field_mapping['fields'][app_field_name] = selected_field
                            st.success(f"Mapped '{app_field_name}' to PDF field '{selected_field}'")
                        else:
                            st.warning("Please enter an application field name")
                
                else:  # Checkbox Group
                    group_name = st.text_input("Checkbox Group Name")
                    option_name = st.text_input("Option Name")
                    if st.button("Save Checkbox Mapping"):
                        if group_name and option_name:
                            if group_name not in st.session_state.field_mapping['checkbox_groups']:
                                st.session_state.field_mapping['checkbox_groups'][group_name] = {}
                            
                            st.session_state.field_mapping['checkbox_groups'][group_name][option_name] = selected_field
                            st.success(f"Mapped checkbox option '{option_name}' in group '{group_name}'")
                        else:
                            st.warning("Please enter both group and option names")
            
            # Show current mapping
            st.subheader("Current Mapping")
            st.json(st.session_state.field_mapping)
            
            if st.button("💾 Save Mapping to Backend"):
                try:
                    response = requests.post(
                        f"{BACKEND_URL}/update-mapping",
                        json=st.session_state.field_mapping
                    )
                    if response.status_code == 200:
                        st.success("Mapping saved to backend!")
                    else:
                        st.error(f"Error saving mapping: {response.text}")
                except requests.exceptions.RequestException as e:
                    st.error(f"Connection error: {str(e)}")

    with tab3:  # API Tester
        st.header("API Testing Interface")
        
        endpoint = st.selectbox(
            "Select Endpoint",
            ["/extract-fields", "/get-mapping", "/fill-pdf"],
            index=0
        )
        
        if endpoint == "/fill-pdf":
            st.subheader("Test PDF Generation")
            json_data = st.text_area("Enter JSON data for PDF filling", height=200, value='''{
    "seller_name": "John Smith",
    "buyer_name": "Jane Doe",
    "purchase_price": 350000
}''')
            
            if st.button("Test /fill-pdf"):
                try:
                    data = json.loads(json_data)
                    response = requests.post(
                        f"{BACKEND_URL}{endpoint}",
                        json=data
                    )
                    
                    st.subheader("Response")
                    st.json({
                        "status_code": response.status_code,
                        "headers": dict(response.headers),
                    })
                    
                    if response.status_code == 200:
                        st.success("PDF generated successfully!")
                        pdf_bytes = BytesIO(response.content)
                        st.download_button(
                            "Download PDF",
                            data=pdf_bytes,
                            file_name="test_output.pdf",
                            mime="application/pdf"
                        )
                    else:
                        st.error(response.text)
                
                except Exception as e:
                    st.error(f"Error: {str(e)}")
        
        else:  # GET endpoints
            if st.button(f"Test {endpoint}"):
                try:
                    response = requests.get(f"{BACKEND_URL}{endpoint}")
                    st.subheader("Response")
                    st.json(response.json())
                except Exception as e:
                    st.error(f"Error: {str(e)}")

if __name__ == "__main__":
    main()