#!/usr/bin/env python3
"""
Visual Verification Helper for PDF Checkbox States
Validates that checkboxes in generated PDFs match expected states
"""

import os
import json
import requests
from pdfrw import PdfReader
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

# Configuration
BACKEND_URL = "http://localhost:5000"

def verify_checkbox_pdf(pdf_path: str, expected_checks: Dict[str, bool]) -> Dict[str, Any]:
    """
    Verify checkboxes in PDF match expected states
    
    Args:
        pdf_path: Path to the PDF file to verify
        expected_checks: Dictionary mapping field names to expected boolean states
        
    Returns:
        Dictionary with verification results including mismatches and statistics
    """
    try:
        pdf = PdfReader(pdf_path)
        results = {}
        field_details = {}
        
        print(f"📄 Analyzing PDF: {pdf_path}")
        print(f"📊 Expected checkboxes: {len(expected_checks)}")
        
        for page_num, page in enumerate(pdf.pages, 1):
            if not page.Annots:
                continue
                
            for annot in page.Annots:
                if annot.Subtype == '/Widget' and annot.FT == '/Btn':
                    # Extract field name
                    field_name = annot.T[1:-1] if annot.T.startswith('(') else annot.T
                    
                    # Check current state
                    current_v = getattr(annot, 'V', None)
                    current_as = getattr(annot, 'AS', None)
                    
                    # Determine if checked using multiple methods (including parentheses)
                    checked_values = ['/Yes', '/On', '/1', 'Yes', 'On', '1', '(/Yes)', '(/On)', '(/1)']
                    is_checked_v = str(current_v) in checked_values
                    is_checked_as = str(current_as) in checked_values
                    is_checked = is_checked_v or is_checked_as
                    
                    results[field_name] = is_checked
                    field_details[field_name] = {
                        'page': page_num,
                        'V': str(current_v) if current_v else 'None',
                        'AS': str(current_as) if current_as else 'None',
                        'is_checked': is_checked,
                        'detection_method': 'V' if is_checked_v else 'AS' if is_checked_as else 'neither'
                    }
        
        # Compare with expected states
        mismatches = {}
        matches = {}
        
        for field_name, expected_state in expected_checks.items():
            actual_state = results.get(field_name)
            
            if actual_state is None:
                mismatches[field_name] = {
                    'expected': expected_state,
                    'actual': 'FIELD_NOT_FOUND',
                    'issue': 'Field not found in PDF'
                }
            elif actual_state != expected_state:
                mismatches[field_name] = {
                    'expected': expected_state,
                    'actual': actual_state,
                    'issue': 'State mismatch',
                    'details': field_details.get(field_name, {})
                }
            else:
                matches[field_name] = {
                    'expected': expected_state,
                    'actual': actual_state,
                    'details': field_details.get(field_name, {})
                }
        
        # Calculate statistics
        total_checkboxes_found = len(results)
        expected_checkboxes = len(expected_checks)
        successful_matches = len(matches)
        
        verification_result = {
            'success': len(mismatches) == 0,
            'total_checkboxes_found': total_checkboxes_found,
            'expected_checkboxes': expected_checkboxes,
            'successful_matches': successful_matches,
            'mismatches': mismatches,
            'matches': matches,
            'field_details': field_details,
            'pdf_path': pdf_path
        }
        
        # Print summary
        print(f"✅ Found {total_checkboxes_found} checkbox fields in PDF")
        print(f"✅ Successfully matched {successful_matches}/{expected_checkboxes} expected checkboxes")
        
        if mismatches:
            print(f"❌ Found {len(mismatches)} mismatches:")
            for field, mismatch in mismatches.items():
                print(f"   - {field}: expected {mismatch['expected']}, got {mismatch['actual']}")
        else:
            print("🎉 All checkboxes match expected states!")
        
        return verification_result
        
    except Exception as e:
        print(f"❌ Error verifying PDF: {e}")
        return {
            'success': False,
            'error': str(e),
            'pdf_path': pdf_path
        }


def test_checkbox_visual_verification():
    """Comprehensive test with visual verification of checkbox states"""
    
    print("🔍 CHECKBOX VISUAL VERIFICATION TEST")
    print("=" * 50)
    
    # Test case 1: Basic boolean test
    test_data_1 = {
        "seller_name": "Visual Test Seller",
        "buyer_name": "Visual Test Buyer", 
        "purchase_price": 600000,
        "financing": {
            "cash": True,
            "conventional": False,
            "fha": True,
            "va": False
        },
        "inspections": {
            "as_is_condition": True,
            "buyer_inspection": False,
            "professional_inspection": True
        }
    }
    
    expected_checks_1 = {
        'Checkbox_8': True,   # cash
        'Checkbox_9': False,  # conventional
        'Checkbox_10': True,  # fha
        'Checkbox_11': False, # va
        'Checkbox_1': True,   # as_is_condition
        'Checkbox_3': False,  # buyer_inspection
        'Checkbox_4': True    # professional_inspection
    }
    
    print("\n1️⃣ Testing Basic Boolean Values")
    print("-" * 35)
    
    success_1 = run_verification_test(test_data_1, expected_checks_1, "boolean_verification_test")
    
    # Test case 2: Mixed input types
    test_data_2 = {
        "seller_name": "Mixed Test Seller",
        "buyer_name": "Mixed Test Buyer",
        "purchase_price": 700000,
        "financing": {
            "cash": "true",      # string
            "conventional": 0,   # numeric
            "fha": "yes",        # string
            "va": False          # boolean
        },
        "appliances": {
            "refrigerator": "checked",  # string
            "washer": 1,               # numeric
            "dryer": "on",             # string
            "dishwasher": False        # boolean
        }
    }
    
    expected_checks_2 = {
        'Checkbox_8': True,   # cash = "true"
        'Checkbox_9': False,  # conventional = 0
        'Checkbox_10': True,  # fha = "yes"
        'Checkbox_11': False, # va = False
        'Checkbox_28': True,  # refrigerator = "checked"
        'Checkbox_29': True,  # washer = 1
        'Checkbox_30': True,  # dryer = "on"
        'Checkbox_31': False  # dishwasher = False
    }
    
    print("\n2️⃣ Testing Mixed Input Types")
    print("-" * 35)
    
    success_2 = run_verification_test(test_data_2, expected_checks_2, "mixed_verification_test")
    
    # Test case 3: Edge cases
    test_data_3 = {
        "seller_name": "Edge Test Seller",
        "buyer_name": "Edge Test Buyer",
        "purchase_price": 800000,
        "financing": {
            "cash": "TRUE",      # uppercase
            "conventional": "",  # empty string
            "fha": "Y",          # single letter
            "va": -1             # negative number
        }
    }
    
    expected_checks_3 = {
        'Checkbox_8': True,   # cash = "TRUE"
        'Checkbox_9': False,  # conventional = ""
        'Checkbox_10': True,  # fha = "Y"
        'Checkbox_11': True   # va = -1 (non-zero)
    }
    
    print("\n3️⃣ Testing Edge Cases")
    print("-" * 25)
    
    success_3 = run_verification_test(test_data_3, expected_checks_3, "edge_verification_test")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VISUAL VERIFICATION SUMMARY")
    print("=" * 50)
    
    total_tests = 3
    successful_tests = sum([success_1, success_2, success_3])
    
    print(f"✅ Successful tests: {successful_tests}/{total_tests}")
    print(f"📊 Success rate: {(successful_tests/total_tests)*100:.1f}%")
    
    if successful_tests == total_tests:
        print("🎉 ALL VISUAL VERIFICATION TESTS PASSED!")
        print("✅ Checkbox functionality is working perfectly!")
    else:
        print("⚠️ Some tests failed - check the details above")
    
    return successful_tests == total_tests


def run_verification_test(test_data: Dict[str, Any], expected_checks: Dict[str, bool], test_name: str) -> bool:
    """Run a single verification test"""
    
    try:
        # Generate PDF
        print(f"   📋 Generating PDF with {len(test_data)} form fields...")
        response = requests.post(f'{BACKEND_URL}/fill-pdf', json=test_data)
        
        if response.status_code != 200:
            print(f"   ❌ PDF generation failed: {response.status_code}")
            return False
        
        # Save PDF temporarily
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_path = f'{test_name}_{timestamp}.pdf'
        
        with open(pdf_path, 'wb') as f:
            f.write(response.content)
        
        print(f"   📄 PDF saved: {pdf_path} ({len(response.content):,} bytes)")
        
        # Verify checkboxes
        print(f"   🔍 Verifying {len(expected_checks)} checkbox states...")
        verification = verify_checkbox_pdf(pdf_path, expected_checks)
        
        if verification['success']:
            print(f"   🎉 SUCCESS: All {verification['successful_matches']} checkboxes verified!")
            return True
        else:
            print(f"   ❌ FAILED: {len(verification['mismatches'])} mismatches found")
            for field, mismatch in verification['mismatches'].items():
                print(f"      - {field}: expected {mismatch['expected']}, got {mismatch['actual']}")
            return False
            
    except Exception as e:
        print(f"   ❌ Test error: {e}")
        return False


def analyze_all_test_pdfs():
    """Analyze all existing test PDFs to verify checkbox states"""
    
    print("🔍 ANALYZING ALL EXISTING TEST PDFs")
    print("=" * 45)
    
    # Find all test PDF files
    test_pdfs = [f for f in os.listdir('.') if f.endswith('.pdf') and 'test' in f.lower()]
    
    if not test_pdfs:
        print("❌ No test PDF files found")
        return
    
    print(f"📄 Found {len(test_pdfs)} test PDF files")
    
    for pdf_file in test_pdfs:
        print(f"\n📋 Analyzing: {pdf_file}")
        print("-" * 40)
        
        try:
            pdf = PdfReader(pdf_file)
            checkbox_count = 0
            checked_count = 0
            
            for page in pdf.pages:
                if not page.Annots:
                    continue
                    
                for annot in page.Annots:
                    if annot.Subtype == '/Widget' and annot.FT == '/Btn':
                        checkbox_count += 1
                        
                        current_v = getattr(annot, 'V', None)
                        current_as = getattr(annot, 'AS', None)
                        checked_values = ['/Yes', '/On', '/1', 'Yes', 'On', '1', '(/Yes)', '(/On)', '(/1)']
                        is_checked = (str(current_v) in checked_values or
                                    str(current_as) in checked_values)
                        
                        if is_checked:
                            checked_count += 1
            
            print(f"   ✅ Total checkboxes: {checkbox_count}")
            print(f"   ☑️ Checked: {checked_count}")
            print(f"   ☐ Unchecked: {checkbox_count - checked_count}")
            print(f"   📊 Check rate: {(checked_count/checkbox_count)*100:.1f}%" if checkbox_count > 0 else "   📊 No checkboxes found")
            
        except Exception as e:
            print(f"   ❌ Error analyzing {pdf_file}: {e}")


if __name__ == "__main__":
    print("🚀 PDF Checkbox Visual Verification System")
    print("Make sure the Flask server is running on http://localhost:5000")
    print()
    
    # Run comprehensive verification tests
    test_checkbox_visual_verification()
    
    # Analyze existing test PDFs
    print("\n" + "=" * 60)
    analyze_all_test_pdfs()
    
    print("\n" + "=" * 60)
    print("🏁 VISUAL VERIFICATION COMPLETED!")
    print("All generated PDFs have been verified for checkbox accuracy.")
