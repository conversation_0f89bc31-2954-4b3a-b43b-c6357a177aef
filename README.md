# PDF Form Filling Service

A robust, production-ready backend service for programmatically filling PDF form templates with comprehensive error handling, field validation, and SaaS-level quality.

## Features

- **Robust PDF Processing**: Enhanced PDF field handling with support for text fields, checkboxes, radio buttons, signatures, and dates
- **Comprehensive Field Mapping**: Intelligent mapping system between frontend form data and PDF fields
- **Advanced Validation**: Input validation, field existence checks, and data type validation
- **Production-Ready Error Handling**: Detailed logging, graceful failure recovery, and comprehensive error reporting
- **SaaS Integration Ready**: RESTful API endpoints with proper request/response formatting
- **Type Safety**: Full type hints and data validation
- **Comprehensive Testing**: Unit tests and integration tests included

## Quick Start

### Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure your PDF template is in place:
```bash
# Your fillable PDF should be named 'as_is_contract_fillable.pdf'
ls as_is_contract_fillable.pdf
```

3. Initialize the field mapping:
```bash
python -c "from app import pdf_form_filler; pdf_form_filler.mapping_manager.create_default_mapping_from_guide()"
```

4. Start the Flask server:
```bash
python app.py
```

The server will start on `http://localhost:5000`

### Basic Usage

```python
import requests

# Sample form data
form_data = {
    "seller_name": "John Smith",
    "buyer_name": "Jane Doe",
    "purchase_price": 350000,
    "financing": {
        "conventional": True,
        "cash": False
    }
}

# Fill PDF
response = requests.post(
    "http://localhost:5000/fill-pdf",
    json=form_data,
    headers={'Content-Type': 'application/json'}
)

if response.status_code == 200:
    with open("filled_contract.pdf", "wb") as f:
        f.write(response.content)
    print("PDF generated successfully!")
```

## API Endpoints

### POST /fill-pdf
Fill PDF template with form data and return the completed PDF.

**Request:**
```json
{
    "seller_name": "John Smith",
    "buyer_name": "Jane Doe",
    "purchase_price": 350000,
    "financing": {
        "cash": true,
        "conventional": false
    }
}
```

**Response:** PDF file download or error JSON

### POST /validate-form
Validate form data without generating PDF.

**Response:**
```json
{
    "status": "success",
    "valid": true,
    "errors": [],
    "warnings": [],
    "processed_fields": 15
}
```

### GET /get-mapping
Get current field mapping configuration.

**Response:**
```json
{
    "status": "success",
    "mapping": {
        "fields": {...},
        "checkbox_groups": {...}
    },
    "total_fields": 25,
    "total_checkbox_groups": 3
}
```

### POST /initialize-mapping
Initialize field mapping from the field mapping guide.

### GET /extract-fields
Extract all fields from the PDF template.

### GET /health
Health check endpoint.

## Architecture

### Core Components

1. **PDFFieldProcessor**: Handles PDF field operations with type-specific handlers
2. **FieldMappingManager**: Manages mappings between form fields and PDF fields
3. **PDFFormFiller**: Main orchestrator with validation and error handling
4. **PDFFieldInfo**: Data class for PDF field metadata

### Field Types Supported

- **Text Fields** (`/Tx`): Regular text input, numbers, dates, emails, phone numbers
- **Button Fields** (`/Btn`): Checkboxes and radio buttons
- **Signature Fields** (`/Sig`): Signature placeholders
- **Choice Fields** (`/Ch`): Dropdowns and listboxes

### Error Handling

The system provides multiple levels of error handling:

1. **Input Validation**: Validates form data structure and types
2. **Field Mapping Validation**: Ensures all fields have valid PDF mappings
3. **PDF Processing Errors**: Handles PDF reading/writing errors
4. **Graceful Degradation**: Continues processing even if some fields fail

## Configuration Files

### pdf_fields.json
Contains metadata for all PDF fields extracted from the template:
```json
{
    "'(Text_1)'": {
        "name": "'(Text_1)'",
        "type": "/Tx",
        "current_value": "()",
        "page": 1
    }
}
```

### field_mapping_guide.json
Provides the mapping between logical field names and PDF field names:
```json
{
    "contract_fields": {
        "parties": {
            "seller_name": "Text_1",
            "buyer_name": "Text_2"
        }
    }
}
```

### field_mapping.json
Runtime field mapping configuration (auto-generated from guide).

## Testing

Run the comprehensive test suite:

```bash
# Run unit tests
python -m unittest test_pdf_filler.py -v

# Run example usage test
python example_usage.py
```

## Logging

The system provides comprehensive logging:

- **INFO**: General operation status
- **WARNING**: Non-critical issues (missing field mappings, etc.)
- **ERROR**: Critical errors that prevent operation
- **DEBUG**: Detailed field processing information

Logs are written to both console and `pdf_filler.log` file.

## Production Deployment

### Environment Variables
```bash
export FLASK_ENV=production
export PDF_TEMPLATE_PATH=/path/to/template.pdf
export LOG_LEVEL=INFO
```

### Docker Deployment
```dockerfile
FROM python:3.9-slim
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
EXPOSE 5000
CMD ["python", "app.py"]
```

### Performance Considerations

- PDF template is loaded once per request (consider caching for high volume)
- Field mapping is loaded at startup and cached
- Memory usage scales with PDF size and number of fields
- Typical processing time: 100-500ms per PDF

## Troubleshooting

### Common Issues

1. **"Field not found" warnings**: Check field mapping configuration
2. **PDF generation fails**: Verify PDF template is valid and accessible
3. **Field values not appearing**: Check field name normalization and type handlers

### Debug Mode

Enable debug logging:
```python
import logging
logging.getLogger('app').setLevel(logging.DEBUG)
```

## Contributing

1. Follow PEP 8 style guidelines
2. Add type hints to all functions
3. Include comprehensive error handling
4. Write unit tests for new features
5. Update documentation

## License

This project is licensed under the MIT License.
