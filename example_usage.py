#!/usr/bin/env python3
"""
Example usage of the enhanced PDF filling system
"""

import json
import requests
from datetime import datetime

# Configuration
BACKEND_URL = "http://localhost:5000"

def test_pdf_filling():
    """Test the PDF filling functionality with sample data"""
    
    # Sample form data for an "As Is" residential contract
    sample_form_data = {
        # Parties
        "seller_name": "<PERSON>",
        "buyer_name": "<PERSON>",
        
        # Property details
        "street_address": "123 Ocean Drive, Miami Beach, FL 33139",
        "county": "Miami-Dade",
        "tax_id": "12-3456-789-0123",
        "legal_description": "Lot 15, Block 3, SUNSET ESTATES SUBDIVISION",
        "additional_details": "Pool, deck, and landscaping included",
        "personal_property_details": "All kitchen appliances and window treatments",
        "excluded_items": "Outdoor furniture and artwork excluded",
        
        # Financial terms
        "purchase_price": 750000,
        "initial_deposit": 75000,
        "contract_date": "2025-07-15",
        
        # Contact information
        "seller_phone": "(*************",
        "seller_email": "<EMAIL>",
        "buyer_phone": "(*************",
        
        # Financing type (checkbox group)
        "financing": {
            "cash": False,
            "conventional": True,
            "fha": False,
            "va": False
        },
        
        # Included items (checkbox group)
        "included_items": {
            "refrigerator": True,
            "washer": True,
            "dryer": True,
            "microwave": True,
            "dishwasher": True,
            "security_system": False
        }
    }
    
    print("Testing PDF Filling System")
    print("=" * 50)
    
    # Test 1: Validate form data
    print("\n1. Validating form data...")
    try:
        response = requests.post(
            f"{BACKEND_URL}/validate-form",
            json=sample_form_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            validation_result = response.json()
            print(f"✓ Validation Status: {validation_result['status']}")
            print(f"✓ Valid: {validation_result['valid']}")
            print(f"✓ Processed Fields: {validation_result['processed_fields']}")
            
            if validation_result['warnings']:
                print(f"⚠ Warnings: {len(validation_result['warnings'])}")
                for warning in validation_result['warnings'][:3]:  # Show first 3
                    print(f"  - {warning}")
            
            if validation_result['errors']:
                print(f"✗ Errors: {len(validation_result['errors'])}")
                for error in validation_result['errors']:
                    print(f"  - {error}")
        else:
            print(f"✗ Validation failed: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Connection error during validation: {e}")
    
    # Test 2: Initialize field mapping if needed
    print("\n2. Checking field mapping...")
    try:
        response = requests.get(f"{BACKEND_URL}/get-mapping")
        if response.status_code == 200:
            mapping_data = response.json()
            total_fields = mapping_data.get('total_fields', 0)
            total_groups = mapping_data.get('total_checkbox_groups', 0)
            
            print(f"✓ Current mapping: {total_fields} fields, {total_groups} checkbox groups")
            
            if total_fields == 0 and total_groups == 0:
                print("  Initializing default mapping...")
                init_response = requests.post(f"{BACKEND_URL}/initialize-mapping")
                if init_response.status_code == 200:
                    init_data = init_response.json()
                    print(f"✓ Initialized: {init_data.get('total_fields', 0)} fields, {init_data.get('total_checkbox_groups', 0)} groups")
                else:
                    print(f"✗ Failed to initialize mapping: {init_response.text}")
        else:
            print(f"✗ Failed to get mapping: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Connection error during mapping check: {e}")
    
    # Test 3: Generate filled PDF
    print("\n3. Generating filled PDF...")
    try:
        response = requests.post(
            f"{BACKEND_URL}/fill-pdf",
            json=sample_form_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            # Save the PDF file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_filled_contract_{timestamp}.pdf"
            
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"✓ PDF generated successfully: {filename}")
            print(f"✓ File size: {len(response.content)} bytes")
            
            # Check response headers for additional info
            if 'X-Fields-Processed' in response.headers:
                print(f"✓ Fields processed: {response.headers['X-Fields-Processed']}")
            if 'X-Fields-Failed' in response.headers:
                print(f"⚠ Fields failed: {response.headers['X-Fields-Failed']}")
                
        else:
            print(f"✗ PDF generation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"  Status: {error_data.get('status', 'unknown')}")
                print(f"  Message: {error_data.get('message', 'No message')}")
                
                if error_data.get('errors'):
                    print("  Errors:")
                    for error in error_data['errors']:
                        print(f"    - {error}")
                
                if error_data.get('warnings'):
                    print("  Warnings:")
                    for warning in error_data['warnings'][:3]:  # Show first 3
                        print(f"    - {warning}")
                        
                print(f"  Fields processed: {error_data.get('fields_processed', 0)}")
                print(f"  Fields failed: {error_data.get('fields_failed', 0)}")
                
            except json.JSONDecodeError:
                print(f"  Raw response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Connection error during PDF generation: {e}")
    
    print("\n" + "=" * 50)
    print("Test completed!")


def test_api_endpoints():
    """Test all API endpoints"""
    
    print("\nTesting API Endpoints")
    print("=" * 30)
    
    endpoints = [
        ("GET", "/health", None),
        ("GET", "/extract-fields", None),
        ("GET", "/get-mapping", None),
    ]
    
    for method, endpoint, data in endpoints:
        print(f"\n{method} {endpoint}")
        try:
            if method == "GET":
                response = requests.get(f"{BACKEND_URL}{endpoint}")
            else:
                response = requests.post(
                    f"{BACKEND_URL}{endpoint}",
                    json=data,
                    headers={'Content-Type': 'application/json'}
                )
            
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if isinstance(result, dict) and 'status' in result:
                        print(f"  Response Status: {result['status']}")
                    
                    # Show relevant info based on endpoint
                    if endpoint == "/extract-fields" and 'total_fields' in result:
                        print(f"  Total Fields: {result['total_fields']}")
                    elif endpoint == "/get-mapping":
                        print(f"  Fields: {result.get('total_fields', 0)}")
                        print(f"  Checkbox Groups: {result.get('total_checkbox_groups', 0)}")
                        
                except json.JSONDecodeError:
                    print(f"  Raw response: {response.text[:100]}...")
            else:
                print(f"  Error: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"  Connection Error: {e}")


if __name__ == "__main__":
    print("PDF Filling System - Example Usage")
    print("Make sure the Flask server is running on http://localhost:5000")
    print()
    
    # Test the system
    test_api_endpoints()
    test_pdf_filling()
