#!/usr/bin/env python3
"""
Comprehensive Checkbox Fix and Verification
Implements the complete solution for visual checkbox rendering
"""

import requests
import json
import os
from datetime import datetime
from pdfrw import PdfReader
from typing import Dict, Any

# Configuration
BACKEND_URL = "http://localhost:5000"

def verify_pdf_checkboxes_enhanced(pdf_path: str, expected_checks: dict) -> dict:
    """Comprehensive PDF checkbox verification with detailed analysis"""
    results = {
        'total_checkboxes': 0,
        'expected_matches': 0,
        'mismatches': {},
        'visual_state': {},
        'field_details': [],
        'success': False
    }
    
    try:
        pdf = PdfReader(pdf_path)
        
        for page_num, page in enumerate(pdf.pages, 1):
            if not page.Annots:
                continue
                
            for annot in page.Annots:
                if annot.Subtype == '/Widget' and annot.FT == '/Btn':
                    field_name = annot.T[1:-1] if annot.T.startswith('(') else annot.T
                    results['total_checkboxes'] += 1
                    
                    # Get current state with multiple detection methods
                    current_value = getattr(annot, 'V', '')
                    current_as = getattr(annot, 'AS', '')
                    
                    # Enhanced checkbox detection - handle field-specific appearance keys
                    checked_values = ['/Yes', '/On', '/1', '(/Yes)', '(/On)', '(/1)', 'Yes', 'On', '1', '//Yes', '//On', '//1']

                    # Check if the value matches the field-specific appearance key (new method)
                    field_name_clean = field_name.replace('(', '').replace(')', '')
                    field_specific_key = f'/{field_name_clean}'

                    is_checked = (str(current_value) in checked_values or
                                str(current_as) in checked_values or
                                str(current_value) == field_specific_key or
                                str(current_as) == field_specific_key)
                    
                    # Analyze appearance dictionary
                    has_ap = hasattr(annot, 'AP') and annot.AP is not None
                    ap_details = {}
                    if has_ap:
                        if hasattr(annot.AP, 'N') and annot.AP.N:
                            ap_details['normal_keys'] = list(annot.AP.N.keys()) if hasattr(annot.AP.N, 'keys') else []
                        if hasattr(annot.AP, 'D') and annot.AP.D:
                            ap_details['down_keys'] = list(annot.AP.D.keys()) if hasattr(annot.AP.D, 'keys') else []
                    
                    # Store detailed field information
                    field_info = {
                        'name': field_name,
                        'page': page_num,
                        'value': str(current_value),
                        'appearance_state': str(current_as),
                        'checked': is_checked,
                        'has_appearance_dict': has_ap,
                        'appearance_details': ap_details
                    }
                    results['field_details'].append(field_info)
                    results['visual_state'][field_name] = is_checked
                    
                    # Check against expected values
                    if field_name in expected_checks:
                        expected = expected_checks[field_name]
                        if is_checked == expected:
                            results['expected_matches'] += 1
                        else:
                            results['mismatches'][field_name] = {
                                'expected': expected,
                                'actual': is_checked,
                                'value': str(current_value),
                                'appearance_state': str(current_as),
                                'details': field_info
                            }
        
        # Calculate success
        total_expected = len(expected_checks)
        results['success'] = len(results['mismatches']) == 0 and results['expected_matches'] == total_expected
        
        print(f"📊 Verification Results:")
        print(f"   Total checkboxes found: {results['total_checkboxes']}")
        print(f"   Expected checkboxes: {total_expected}")
        print(f"   Successful matches: {results['expected_matches']}")
        print(f"   Mismatches: {len(results['mismatches'])}")
        
        if results['mismatches']:
            print(f"❌ Mismatches found:")
            for field, mismatch in results['mismatches'].items():
                print(f"   - {field}: expected {mismatch['expected']}, got {mismatch['actual']} (V={mismatch['value']}, AS={mismatch['appearance_state']})")
        else:
            print(f"✅ All expected checkboxes verified successfully!")
    
    except Exception as e:
        print(f"❌ PDF verification error: {str(e)}")
        results['error'] = str(e)
        
    return results


def test_comprehensive_checkbox_fix():
    """Test the comprehensive checkbox fix with visual verification"""
    
    print("🔧 COMPREHENSIVE CHECKBOX FIX TEST")
    print("=" * 50)
    
    # Step 1: Debug current checkbox state
    print("\n1️⃣ Debugging Current Checkbox State")
    print("-" * 40)
    
    try:
        debug_response = requests.get(f'{BACKEND_URL}/debug-checkboxes')
        if debug_response.status_code == 200:
            debug_data = debug_response.json()
            print(f"✅ Found {debug_data['total_checkboxes']} total checkboxes")
            print(f"📊 Mapped: {debug_data['summary']['mapped_checkboxes']}")
            print(f"📊 Unmapped: {debug_data['summary']['unmapped_checkboxes']}")
            print(f"📊 Currently checked: {debug_data['summary']['currently_checked']}")
            
            # Show some example checkboxes
            print(f"\n🔍 Sample checkbox fields:")
            for i, checkbox in enumerate(debug_data['checkboxes'][:5]):
                status = "✅ MAPPED" if checkbox['in_mapping'] else "❌ UNMAPPED"
                checked = "☑️ CHECKED" if checkbox['is_checked'] else "☐ UNCHECKED"
                print(f"   {checkbox['name']}: {status}, {checked}")
                if checkbox['in_mapping']:
                    print(f"      → Mapped to: {checkbox['mapped_to']}")
        else:
            print(f"❌ Debug request failed: {debug_response.status_code}")
    except Exception as e:
        print(f"❌ Debug error: {e}")
    
    # Step 2: Test with comprehensive data
    print("\n2️⃣ Testing Enhanced Checkbox Processing")
    print("-" * 45)
    
    test_data = {
        "seller_name": "Enhanced Test Seller",
        "buyer_name": "Enhanced Test Buyer",
        "purchase_price": 850000,
        
        # Test financing checkboxes
        "financing": {
            "cash": True,
            "conventional": False,
            "fha": True,
            "va": False,
            "usda": False,
            "other": False
        },
        
        # Test inspection checkboxes
        "inspections": {
            "as_is_condition": True,
            "seller_repairs": False,
            "buyer_inspection": True,
            "professional_inspection": True,
            "termite_inspection": False,
            "roof_inspection": True
        },
        
        # Test appliance checkboxes
        "appliances": {
            "refrigerator": True,
            "washer": False,
            "dryer": True,
            "dishwasher": True,
            "microwave": False,
            "oven_range": True
        }
    }
    
    # Generate PDF with enhanced processing
    print("📄 Generating PDF with enhanced checkbox processing...")
    try:
        response = requests.post(
            f'{BACKEND_URL}/fill-pdf',
            json=test_data,
            headers={
                'Content-Type': 'application/json',
                'X-Debug-Checkboxes': 'true'  # Enable debug mode
            }
        )
        
        if response.status_code == 200:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            pdf_path = f'enhanced_checkbox_test_{timestamp}.pdf'
            
            with open(pdf_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ PDF generated: {pdf_path} ({len(response.content):,} bytes)")
            
            # Step 3: Comprehensive verification
            print("\n3️⃣ Comprehensive Visual Verification")
            print("-" * 40)
            
            # Define expected checkbox states based on our test data
            expected_checks = {
                # Financing group
                'Checkbox_8': True,   # cash
                'Checkbox_9': False,  # conventional
                'Checkbox_10': True,  # fha
                'Checkbox_11': False, # va
                'Checkbox_12': False, # usda
                'Checkbox_13': False, # other
                
                # Inspections group
                'Checkbox_1': True,   # as_is_condition
                'Checkbox_2': False,  # seller_repairs
                'Checkbox_3': True,   # buyer_inspection
                'Checkbox_4': True,   # professional_inspection
                'Checkbox_5': False,  # termite_inspection
                'Checkbox_6': True,   # roof_inspection
                
                # Appliances group
                'Checkbox_28': True,  # refrigerator
                'Checkbox_29': False, # washer
                'Checkbox_30': True,  # dryer
                'Checkbox_31': True,  # dishwasher
                'Checkbox_32': False, # microwave
                'Checkbox_33': True   # oven_range
            }
            
            verification = verify_pdf_checkboxes_enhanced(pdf_path, expected_checks)
            
            if verification['success']:
                print(f"🎉 SUCCESS! All {verification['expected_matches']} checkboxes verified!")
                print(f"✅ Visual rendering fix is working!")
                
                # Show checkbox statistics
                total_checked = sum(1 for checked in verification['visual_state'].values() if checked)
                total_unchecked = len(verification['visual_state']) - total_checked
                print(f"📊 Total checkboxes in PDF: {verification['total_checkboxes']}")
                print(f"📊 Visually checked: {total_checked}")
                print(f"📊 Visually unchecked: {total_unchecked}")
                
                return True
            else:
                print(f"❌ VERIFICATION FAILED!")
                print(f"   Expected matches: {len(expected_checks)}")
                print(f"   Actual matches: {verification['expected_matches']}")
                print(f"   Mismatches: {len(verification['mismatches'])}")
                
                return False
                
        else:
            print(f"❌ PDF generation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('message', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False


def analyze_checkbox_rendering():
    """Analyze why checkboxes might not be rendering visually"""
    
    print("\n🔍 CHECKBOX RENDERING ANALYSIS")
    print("=" * 40)
    
    # Find the most recent test PDF
    test_pdfs = [f for f in os.listdir('.') if f.endswith('.pdf') and 'test' in f.lower()]
    if not test_pdfs:
        print("❌ No test PDFs found for analysis")
        return
    
    # Sort by modification time and get the most recent
    test_pdfs.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    latest_pdf = test_pdfs[0]
    
    print(f"📄 Analyzing: {latest_pdf}")
    
    try:
        pdf = PdfReader(latest_pdf)
        checkbox_analysis = {
            'total_checkboxes': 0,
            'with_appearance_dict': 0,
            'checked_checkboxes': 0,
            'state_patterns': {},
            'appearance_patterns': {}
        }
        
        for page in pdf.pages:
            if not page.Annots:
                continue
                
            for annot in page.Annots:
                if annot.Subtype == '/Widget' and annot.FT == '/Btn':
                    checkbox_analysis['total_checkboxes'] += 1
                    
                    # Analyze current state
                    current_v = getattr(annot, 'V', 'None')
                    current_as = getattr(annot, 'AS', 'None')
                    
                    state_key = f"V:{current_v}, AS:{current_as}"
                    checkbox_analysis['state_patterns'][state_key] = checkbox_analysis['state_patterns'].get(state_key, 0) + 1
                    
                    # Check if checked - include field-specific appearance keys
                    checked_values = ['/Yes', '/On', '/1', '(/Yes)', '(/On)', '(/1)']

                    # Check for field-specific appearance key
                    field_name = annot.T[1:-1] if annot.T.startswith('(') else annot.T
                    field_specific_key = f'/{field_name}'

                    if (str(current_v) in checked_values or str(current_as) in checked_values or
                        str(current_v) == field_specific_key or str(current_as) == field_specific_key):
                        checkbox_analysis['checked_checkboxes'] += 1
                    
                    # Analyze appearance dictionary
                    if hasattr(annot, 'AP') and annot.AP:
                        checkbox_analysis['with_appearance_dict'] += 1
                        
                        ap_info = []
                        if hasattr(annot.AP, 'N') and annot.AP.N:
                            ap_info.append(f"N:{list(annot.AP.N.keys()) if hasattr(annot.AP.N, 'keys') else 'no_keys'}")
                        if hasattr(annot.AP, 'D') and annot.AP.D:
                            ap_info.append(f"D:{list(annot.AP.D.keys()) if hasattr(annot.AP.D, 'keys') else 'no_keys'}")
                        
                        ap_key = ", ".join(ap_info) if ap_info else "empty"
                        checkbox_analysis['appearance_patterns'][ap_key] = checkbox_analysis['appearance_patterns'].get(ap_key, 0) + 1
        
        # Print analysis results
        print(f"📊 Analysis Results:")
        print(f"   Total checkboxes: {checkbox_analysis['total_checkboxes']}")
        print(f"   With appearance dict: {checkbox_analysis['with_appearance_dict']}")
        print(f"   Checked checkboxes: {checkbox_analysis['checked_checkboxes']}")
        
        print(f"\n📈 State Patterns:")
        for pattern, count in sorted(checkbox_analysis['state_patterns'].items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"   {pattern}: {count} checkboxes")
        
        print(f"\n🎨 Appearance Patterns:")
        for pattern, count in sorted(checkbox_analysis['appearance_patterns'].items(), key=lambda x: x[1], reverse=True)[:3]:
            print(f"   {pattern}: {count} checkboxes")
        
        # Provide recommendations
        print(f"\n💡 Recommendations:")
        if checkbox_analysis['checked_checkboxes'] == 0:
            print("   ❌ No checkboxes appear checked - values may not be set correctly")
        elif checkbox_analysis['checked_checkboxes'] < 5:
            print("   ⚠️ Few checkboxes checked - partial success")
        else:
            print("   ✅ Multiple checkboxes checked - enhancement is working!")
        
        if checkbox_analysis['with_appearance_dict'] < checkbox_analysis['total_checkboxes'] * 0.8:
            print("   ⚠️ Many checkboxes missing appearance dictionaries")
        else:
            print("   ✅ Most checkboxes have appearance dictionaries")
        
    except Exception as e:
        print(f"❌ Analysis error: {e}")


if __name__ == "__main__":
    print("🚀 Comprehensive Checkbox Fix and Verification")
    print("Make sure the Flask server is running on http://localhost:5000")
    print()
    
    # Run the comprehensive test
    success = test_comprehensive_checkbox_fix()
    
    # Analyze rendering
    analyze_checkbox_rendering()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 COMPREHENSIVE CHECKBOX FIX SUCCESSFUL!")
        print("✅ Visual checkbox rendering is now working correctly!")
    else:
        print("⚠️ CHECKBOX FIX NEEDS FURTHER INVESTIGATION")
        print("📋 Check the analysis above for specific issues")
    
    print("🏁 Test completed!")
