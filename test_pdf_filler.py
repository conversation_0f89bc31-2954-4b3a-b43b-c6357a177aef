#!/usr/bin/env python3
"""
Comprehensive test suite for the PDF filling system
"""

import unittest
import json
import os
import tempfile
import io
from unittest.mock import patch, MagicMock
import sys

# Add the current directory to the path to import app modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import PDFFormFiller, PDFFieldProcessor, FieldMappingManager, PDFFieldInfo


class TestPDFFieldInfo(unittest.TestCase):
    """Test PDFFieldInfo data class"""
    
    def test_field_info_creation(self):
        """Test creating PDFFieldInfo objects"""
        field_info = PDFFieldInfo(
            name="'(Text_1)'",
            field_type="/Tx",
            current_value="",
            page=1
        )
        
        self.assertEqual(field_info.name, "'(Text_1)'")
        self.assertEqual(field_info.field_type, "/Tx")
        self.assertEqual(field_info.normalized_name, "Text_1")
        self.assertEqual(field_info.page, 1)
    
    def test_field_name_normalization(self):
        """Test field name normalization"""
        test_cases = [
            ("'(Text_1)'", "Text_1"),
            ("(Checkbox_1)", "Checkbox_1"),
            ("Text_1", "Text_1"),
            ("'(Number_1)'", "Number_1")
        ]
        
        for input_name, expected_output in test_cases:
            normalized = PDFFieldInfo.normalize_field_name(input_name)
            self.assertEqual(normalized, expected_output)


class TestFieldMappingManager(unittest.TestCase):
    """Test FieldMappingManager functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.field_mapping_path = os.path.join(self.temp_dir, 'field_mapping.json')
        self.field_mapping_guide_path = os.path.join(self.temp_dir, 'field_mapping_guide.json')
        self.pdf_fields_path = os.path.join(self.temp_dir, 'pdf_fields.json')
        
        # Create test data files
        self._create_test_files()
        
        self.mapping_manager = FieldMappingManager(
            self.field_mapping_path,
            self.field_mapping_guide_path,
            self.pdf_fields_path
        )
    
    def _create_test_files(self):
        """Create test data files"""
        # Field mapping
        field_mapping = {
            "fields": {
                "seller_name": "'(Text_1)'",
                "buyer_name": "'(Text_2)'"
            },
            "checkbox_groups": {
                "financing": {
                    "cash": "'(Checkbox_8)'",
                    "conventional": "'(Checkbox_9)'"
                }
            }
        }
        
        with open(self.field_mapping_path, 'w') as f:
            json.dump(field_mapping, f)
        
        # Field mapping guide
        field_mapping_guide = {
            "contract_fields": {
                "parties": {
                    "seller_name": "Text_1",
                    "buyer_name": "Text_2"
                },
                "financing_checkboxes": {
                    "cash": "Checkbox_8",
                    "conventional": "Checkbox_9"
                }
            }
        }
        
        with open(self.field_mapping_guide_path, 'w') as f:
            json.dump(field_mapping_guide, f)
        
        # PDF fields
        pdf_fields = {
            "'(Text_1)'": {
                "name": "'(Text_1)'",
                "type": "/Tx",
                "current_value": "()",
                "page": 1
            },
            "'(Text_2)'": {
                "name": "'(Text_2)'",
                "type": "/Tx",
                "current_value": "()",
                "page": 1
            },
            "'(Checkbox_8)'": {
                "name": "'(Checkbox_8)'",
                "type": "/Btn",
                "current_value": "",
                "page": 1
            },
            "'(Checkbox_9)'": {
                "name": "'(Checkbox_9)'",
                "type": "/Btn",
                "current_value": "",
                "page": 1
            }
        }
        
        with open(self.pdf_fields_path, 'w') as f:
            json.dump(pdf_fields, f)
    
    def test_load_field_mapping(self):
        """Test loading field mapping"""
        self.assertIn('seller_name', self.mapping_manager.field_mapping['fields'])
        self.assertEqual(
            self.mapping_manager.field_mapping['fields']['seller_name'],
            "'(Text_1)'"
        )
    
    def test_get_pdf_field_for_form_field(self):
        """Test getting PDF field for form field"""
        pdf_field = self.mapping_manager.get_pdf_field_for_form_field('seller_name')
        self.assertEqual(pdf_field, "'(Text_1)'")
        
        # Test non-existent field
        pdf_field = self.mapping_manager.get_pdf_field_for_form_field('non_existent')
        self.assertIsNone(pdf_field)
    
    def test_validate_form_data(self):
        """Test form data validation"""
        form_data = {
            'seller_name': 'John Doe',
            'buyer_name': 'Jane Smith',
            'financing': {
                'cash': True,
                'conventional': False
            },
            'unknown_field': 'some value'
        }
        
        validation_result = self.mapping_manager.validate_form_data(form_data)
        
        self.assertIsInstance(validation_result, dict)
        self.assertIn('errors', validation_result)
        self.assertIn('warnings', validation_result)
        
        # Should have warning for unknown_field
        self.assertTrue(any('unknown_field' in warning for warning in validation_result['warnings']))
    
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir)


class TestPDFFormFiller(unittest.TestCase):
    """Test PDFFormFiller functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        
        # Mock the file paths
        self.original_paths = {
            'PDF_TEMPLATE_PATH': 'as_is_contract_fillable.pdf',
            'FIELD_MAPPING_PATH': 'field_mapping.json',
            'PDF_FIELDS_PATH': 'pdf_fields.json',
            'FIELD_MAPPING_GUIDE_PATH': 'field_mapping_guide.json'
        }
        
        # Create minimal test files
        self._create_minimal_test_files()
    
    def _create_minimal_test_files(self):
        """Create minimal test files for testing"""
        # Create empty field mapping
        field_mapping = {"fields": {}, "checkbox_groups": {}}
        with open('field_mapping.json', 'w') as f:
            json.dump(field_mapping, f)
        
        # Create minimal PDF fields
        pdf_fields = {
            "'(Text_1)'": {
                "name": "'(Text_1)'",
                "type": "/Tx",
                "current_value": "()",
                "page": 1
            }
        }
        with open('pdf_fields.json', 'w') as f:
            json.dump(pdf_fields, f)
        
        # Create minimal field mapping guide
        field_mapping_guide = {"contract_fields": {}}
        with open('field_mapping_guide.json', 'w') as f:
            json.dump(field_mapping_guide, f)
    
    def test_validate_input_data(self):
        """Test input data validation"""
        # Mock the PDFFormFiller to avoid file dependencies
        with patch('app.PDFFieldProcessor'), \
             patch('app.FieldMappingManager'):
            
            filler = PDFFormFiller()
            
            # Test empty data
            result = filler.validate_input_data({})
            self.assertFalse(result['valid'])
            self.assertIn('No form data provided', result['errors'])
            
            # Test valid data
            form_data = {'seller_name': 'John Doe'}
            result = filler.validate_input_data(form_data)
            self.assertIsInstance(result, dict)
            self.assertIn('valid', result)
            self.assertIn('errors', result)
            self.assertIn('warnings', result)
            self.assertIn('processed_data', result)
    
    def test_process_field_value(self):
        """Test field value processing"""
        with patch('app.PDFFieldProcessor'), \
             patch('app.FieldMappingManager'):
            
            filler = PDFFormFiller()
            
            # Test text value
            result = filler._process_field_value('seller_name', 'John Doe')
            self.assertEqual(result, 'John Doe')
            
            # Test number value
            result = filler._process_field_value('purchase_price', 350000)
            self.assertEqual(result, '350000')
            
            # Test checkbox group
            checkbox_data = {'cash': True, 'conventional': False}
            result = filler._process_checkbox_group_value(checkbox_data)
            self.assertEqual(result, {'cash': True, 'conventional': False})
    
    def tearDown(self):
        """Clean up test files"""
        test_files = ['field_mapping.json', 'pdf_fields.json', 'field_mapping_guide.json']
        for file in test_files:
            if os.path.exists(file):
                os.remove(file)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def test_system_initialization(self):
        """Test that the system initializes correctly"""
        # This test ensures all components can be imported and initialized
        try:
            from app import pdf_form_filler
            self.assertIsNotNone(pdf_form_filler)
            self.assertIsNotNone(pdf_form_filler.pdf_processor)
            self.assertIsNotNone(pdf_form_filler.mapping_manager)
        except Exception as e:
            self.fail(f"System initialization failed: {e}")


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
